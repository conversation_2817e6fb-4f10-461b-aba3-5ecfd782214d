<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮闪退问题修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-button {
            background: linear-gradient(92deg, #3363ff 0%, #775fdd 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(51, 99, 255, 0.3);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-bottom: 10px;
        }
        
        .fix-list {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
        }
        
        .fix-list h4 {
            color: #155724;
            margin-top: 0;
        }
        
        .fix-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .fix-list li {
            margin: 8px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 按钮闪退问题修复测试</h1>
        
        <div class="test-section">
            <h3>📋 问题描述</h3>
            <p>在 <code>&lt;f-base-button /&gt;</code> 组件中长按鼠标左键约400-900ms后松开，会导致应用闪退。</p>
            <p><strong>问题原因：</strong>CSS性能瓶颈导致，主要包括：</p>
            <ul>
                <li>大量@property CSS自定义属性导致重绘重排</li>
                <li>复杂的blur滤镜效果消耗大量GPU资源</li>
                <li>pay组件加载时机与按钮动画冲突</li>
                <li>缺少防抖处理导致重复调用</li>
            </ul>
        </div>

        <div class="fix-list">
            <h4>🛠️ 修复方案</h4>
            <ul>
                <li><strong>优化按钮CSS性能：</strong>减少blur滤镜值(15px→6px)，简化过渡动画时间</li>
                <li><strong>简化pay组件CSS：</strong>大幅减少@property属性数量，优化过渡效果</li>
                <li><strong>添加防抖处理：</strong>在goSettle方法中添加100ms防抖，避免重复调用</li>
                <li><strong>优化组件加载时机：</strong>延迟pay组件加载150ms，避免与按钮动画冲突</li>
                <li><strong>使用requestAnimationFrame：</strong>优化DOM操作时机</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试按钮</h3>
            <p>请按住下面的按钮约500ms后松开，观察是否还会闪退：</p>
            <button class="test-button" id="testButton">测试按钮 - 模拟去结算</button>
            <div id="status" class="status" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试日志</h3>
            <div id="logArea" class="log-area"></div>
            <button onclick="clearLog()" style="padding: 8px 16px; border: 1px solid #ccc; border-radius: 4px; background: white; cursor: pointer;">清空日志</button>
        </div>

        <div class="test-section">
            <h3>✅ 预期结果</h3>
            <ul>
                <li>长按按钮500ms后松开，应用不会闪退</li>
                <li>按钮动画流畅，无卡顿现象</li>
                <li>防抖处理生效，避免重复调用</li>
                <li>组件加载延迟生效，避免冲突</li>
            </ul>
        </div>
    </div>

    <script>
        let pressStartTime = 0;
        let logCount = 0;
        const testButton = document.getElementById('testButton');
        const statusDiv = document.getElementById('status');
        const logArea = document.getElementById('logArea');

        function log(message) {
            logCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${logCount}. ${message}\n`;
            logArea.textContent += logMessage;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function showStatus(message, type = 'success') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        function clearLog() {
            logArea.textContent = '';
            logCount = 0;
            statusDiv.style.display = 'none';
        }

        // 模拟修复后的goSettle方法
        let lastGoSettleTime = 0;
        function fixedGoSettle() {
            const now = Date.now();
            
            // 防抖处理
            if (lastGoSettleTime && (now - lastGoSettleTime) < 100) {
                log("🚫 防抖处理：忽略重复调用");
                showStatus("防抖处理生效，忽略重复调用", "warning");
                return;
            }
            lastGoSettleTime = now;
            
            log("✅ 执行修复后的goSettle方法");
            showStatus("修复后的方法执行成功，无闪退现象", "success");
            
            // 模拟延迟加载pay组件
            setTimeout(() => {
                log("📦 延迟150ms加载pay组件，避免与按钮动画冲突");
            }, 150);
        }

        // 按钮事件处理
        testButton.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // 左键
                pressStartTime = performance.now();
                log("🖱️ 鼠标左键按下");
            }
        });

        testButton.addEventListener('mouseup', (e) => {
            if (e.button === 0) { // 左键
                const pressDuration = performance.now() - pressStartTime;
                log(`🖱️ 鼠标左键松开，按压时长: ${pressDuration.toFixed(0)}ms`);
                
                if (pressDuration >= 400 && pressDuration <= 900) {
                    log("⚠️ 检测到问题时长范围（400-900ms），执行修复后的方法");
                    fixedGoSettle();
                } else {
                    log("✅ 正常点击，执行修复后的方法");
                    fixedGoSettle();
                }
            }
        });

        // 初始化日志
        log("🚀 测试页面已加载，准备测试按钮修复效果");
        log("📝 请按住测试按钮约500ms后松开");
        log("🔍 观察是否还会出现闪退现象");
    </script>
</body>
</html>
