"use strict";
const lodash = require("lodash");
Vue.component("app-bill-page", function (resolve, reject) {
  $.get("component/billPage/billPage.html").then(function (res) {
    resolve({
      template: res,
      props: {
        login: {
          type: Object,
        },
        handle_foucs_input: {
          type: Boolean,
          value: false,
        },
        "transition-direction": {
          type: String,
          value: "slide-down",
        },
      },
      data: function () {
        return {
          timerManager: ff_util.timerManager,
          goSettleLoading: false,
          lastGoSettleTime: 0, // 防抖处理时间戳
          billingType: "0",
          isShowMemberSearch: false, // 控制会员搜索弹框显示
          cashier_open_order_service_name: [], // 开单--服务
          cashier_open_order_product_name: [], //开单--产品
          //选择赠送
          isChooseGift: false, //选择赠送的弹框
          billGiftData: [],
          showGiftData: {
            allPrice: 0,
            serverNum: 0,
            productNum: 0,
          },
          surplus: 0, //店铺的赠送额度
          isShowLoseEfficacyCard: true,
          sampleMemberCardInfo: [], //单独抽出所有权益中的卡
          sampleMemberCardInfo_runOut: [], // 用完的服务
          timerCardUse: [], // 次数权益使用情况
          which_server_technician: null, //用来确定添加服务人员是给哪一个服务添加服务人员
          C_open_order_specifications_save: [], //选择规格后或者选择服务后保留的数据
          ji_shi_zhanshi: [], //用来循环展示服务人员姓名
          ji_shis: [], //服务人员信息循环数据
          xiao_shou: false, //判断弹出框
          which_server_Sale: null, //判断给哪个添加销售
          isShowMergeDialog: false,
          pi_is_xiao_name: "", //给服务添加字段显示选中的销售姓名
          xiao_shou_save: [], //存取选中销售的相关数据
          pi_is_ji_name: "", //给服务添加字段显示选中的服务人员姓名
          ji_shi_save: [], //存取选中服务人员的相关数据
          isCouponCard: false,
          assignGoods: false, //指定商品，无门槛
          payTotal: 0,
          balanceCardUse: [], // 余额耗卡使用情况
          C_open_order_Specifications: {},
          cashier_open_order_service_choice: [], //选择服务后保留的服务数据
          data_server_product: null, //定义一个变量用来给确定规格判断是服务还是产品的数据

          //订单号
          orderNo: "",
          //充次卡下单完智慧跳转支付页面
          isRechargeCard: false,
          //收银台--充次卡收款跳转到收银台。 0:会员余额,1:支付宝/微信,2:现金,4:自定义收款
          isPayStatus: 4,
          //收银台到支付页面的标志位
          billToPay: 0,
          buy_receipt: false,
          //下单时候需要的充次卡信息
          extraData: {},
          //loading页面转圈圈
          loading: false,
          url: baseUrl,
          //循环的服务列表
          cashier_open_order_service_name: [],
          //搜索的关键字
          search_keyword: "",
          //备注信息
          beizhu_info: "",
          //最后需要提交支付的值
          pay_all: 0,
          pay_all_show: "0.00",
          loginInfo: {}, // 登录信息
          serverLabelid: "",
          isServerScroll: false,
          serverAllCount: 0,
          busy: false,
          loadingtip: "加载中···",
          memberInfo: {},
          //判断是不是展示销售
          zhk_xiao_shou: false,
          //存贮销售内容
          xiaoshous: [],
          //销售用来存储以选择的销售信息。
          xiao_shou_zhanshi: [],
          //在页面展示选中的销售内容
          zhk_xiao_shou_zhanshi: [],
          //页面展示销售的变量
          SalesShow: "",
          selesShows: "请选择",
          giftPickTimeOptions: {
            disabledDate(time) {
              return time.getTime() < Date.now() - 8.64e7; //如果没有后面的-8.64e7就是不可以选择今天的
            },
          },
          // 选择协助员工
          helpStaffArr: {},
          helpStaffAll: [],
          helpStaffVisible: false,
          checkHelpStaffArr: [],
          bindStaffId: 0,
          isactive1: 4,
          // 有效期等
          cardinfo: {
            permanent: "2",
            validity_time: "",
            effectiveMethod: "1",
            dueDate: "",
          },
          // 左键长按相关变量
          longPressTimer: null,
          longPressInterval: null,
          longPressFirstTimer: null,
          isLongPressing: false,
          wasLongPressed: false,
          leftPressData: null,
          leftPressEvent: null,
          leftPressStartTime: 0,
          isLeftPressed: false,
          // 右键长按相关变量
          rightLongPressTimer: null,
          rightLongPressInterval: null,
          rightLongPressFirstTimer: null,
          isRightLongPressing: false,
          wasRightLongPressed: false,
          rightLongPressData: null,
          rightLongPressEvent: null,
          rightPressStartTime: 0,
          isRightPressed: false,
        };
      },
      mounted: function () {
        try {
          // 检查必要的依赖
          if (!this.timerManager) {
            console.error("timerManager 未初始化");
            return;
          }

          // 确保DOM已完全加载
          this.$nextTick(() => {
            this.serviceList();
            // this.setupGlobalMouseEvents();
          });
        } catch (error) {
          console.error("组件挂载时出错:", error);
        }
      },

      methods: {
        // 全局错误处理方法
        handleError: function (error, context = "") {
          console.error(`${context} 发生错误:`, error);

          // 显示用户友好的错误信息
          if (this.$message) {
            this.$message({
              type: "error",
              message: "操作失败，请重试",
              duration: 2000,
            });
          }
        },

        // 安全执行方法的包装器
        safeExecute: function (fn, context = "") {
          try {
            return fn();
          } catch (error) {
            this.handleError(error, context);
            return null;
          }
        },

        // 获取服务卡片元素
        getServiceCards: function () {
          try {
            // 增加更严格的检查
            if (
              this.$refs &&
              this.$refs.serviceCardsList &&
              this.$refs.serviceCardsList.$el &&
              typeof this.$refs.serviceCardsList.$el.querySelectorAll ===
                "function"
            ) {
              const elements =
                this.$refs.serviceCardsList.$el.querySelectorAll(
                  ".o-service-card"
                );
              return elements ? Array.from(elements) : [];
            }
            return [];
          } catch (error) {
            console.error("getServiceCards 查询出错:", error);
            return [];
          }
        },

        // 获取搜索卡片元素
        getSearchCardBoxes: function () {
          try {
            // 增加更严格的检查
            if (
              this.$refs &&
              this.$refs.cardWrap &&
              typeof this.$refs.cardWrap.querySelectorAll === "function"
            ) {
              const elements =
                this.$refs.cardWrap.querySelectorAll("#searchCardBox");
              return elements ? Array.from(elements) : [];
            }
            return [];
          } catch (error) {
            console.error("getSearchCardBoxes 查询出错:", error);
            return [];
          }
        },

        // 安全的数组操作包装器
        safeArrayOperation: function (array, operation, ...args) {
          try {
            if (!Array.isArray(array)) {
              console.warn("safeArrayOperation: 不是有效数组");
              return null;
            }
            return operation.apply(array, args);
          } catch (error) {
            console.error("safeArrayOperation 出错:", error);
            return null;
          }
        },

        // 安全的对象属性访问
        safeGetProperty: function (obj, path, defaultValue = null) {
          try {
            if (!obj || typeof obj !== "object") {
              return defaultValue;
            }
            const keys = path.split(".");
            let current = obj;
            for (const key of keys) {
              if (
                current === null ||
                current === undefined ||
                !(key in current)
              ) {
                return defaultValue;
              }
              current = current[key];
            }
            return current;
          } catch (error) {
            console.error("safeGetProperty 出错:", error);
            return defaultValue;
          }
        },

        // 高亮搜索关键词
        highlightSearchKeyword: function (text) {
          if (!this.search_keyword || !text) {
            return text;
          }

          // 转义特殊字符，防止正则表达式错误
          const escapedKeyword = this.search_keyword.replace(
            /[.*+?^${}()|[\]\\]/g,
            "\\$&"
          );
          const regex = new RegExp(`(${escapedKeyword})`, "gi");

          return text.replace(
            regex,
            '<span class="o-search-highlight">$1</span>'
          );
        },
        getOrderTypeName(id) {
          return ff_util.OrderType.getOrderTypeName(id);
        },
        getOrderTypeColor(id) {
          return ff_util.OrderType.getOrderTypeColor(id);
        },
        deepCopy(obj) {
          try {
            // 使用 lodash 的 cloneDeep 进行深拷贝，更安全且性能更好
            return lodash.cloneDeep(obj);
          } catch (error) {
            console.error("deepCopy 使用 lodash.cloneDeep 失败:", error);
            // 如果 lodash 不可用，回退到 JSON 方法（有限制但更安全）
            try {
              return JSON.parse(JSON.stringify(obj));
            } catch (jsonError) {
              console.error("deepCopy JSON 方法也失败:", jsonError);
              // 最后的回退，返回原对象（至少不会崩溃）
              return obj;
            }
          }
        },

        // 收款 - 添加防抖处理
        goSettle: function () {
          console.log("🚀 ~ flag:");
          if (this.goSettleLoading) return;

          // 防抖处理：如果在100ms内重复调用，则忽略
          const now = Date.now();
          if (this.lastGoSettleTime && now - this.lastGoSettleTime < 100) {
            console.log("🚀 ~ goSettle 防抖处理：忽略重复调用");
            return;
          }
          this.lastGoSettleTime = now;

          try {
            var _self = this;
            var save_orderData = _self.C_open_order_specifications_save;

            if (!save_orderData || save_orderData.length <= 0) {
              return this.$message({
                type: "warning",
                message: "订单信息不能为空",
                duration: 1500,
              });
            }
            _self.goSettleLoading = true;
            var buyerId = 0;
            if (this.memberInfo && this.memberInfo.id) {
              buyerId = this.memberInfo.id;
            }
            var orderItems = [];
            let isCostServer = false; //判断购物车里有没有服务
            for (var i = 0; i < save_orderData.length; i++) {
              // zhonglei：1 服务 2产品 3卡项 4充值
              if (save_orderData[i].zhonglei != 2) {
                isCostServer = true;
              }
              orderItems.push({
                cardName:
                  save_orderData[i].manualDiscountCard["cardName"] || "",
                cardDetailsId:
                  save_orderData[i].manualDiscountCard.cardSource != -1
                    ? save_orderData[i].manualDiscountCard["cardDetailsId"] ||
                      save_orderData[i].manualDiscountCard["id"] ||
                      0
                    : save_orderData[i].manualDiscount == 3
                      ? save_orderData[i].manualDiscountCard["cardDetailsId"] ||
                        save_orderData[i].manualDiscountCard["id"] ||
                        0
                      : 0, // (开单使用)使用卡项详情的id
                cardId:
                  save_orderData[i].manualDiscountCard.cardSource != -1
                    ? save_orderData[i].manualDiscountCard["membercard_id"] || 0
                    : save_orderData[i].manualDiscount == 3
                      ? save_orderData[i].manualDiscountCard["membercard_id"] ||
                        0
                      : 0, // 卡项id

                discount:
                  save_orderData[i].manualDiscountCard["discount"] || "10", // 折扣（开单选择充值卡有）

                consumeCard: 1, //是否耗卡（1,不是，2，是），已经没用
                consumeCardId:
                  save_orderData[i].costCard &&
                  save_orderData[i].costCard["membercard_id"]
                    ? save_orderData[i].costCard["membercard_id"]
                    : 0, //耗卡的卡项id（consumeCard==2时有效）默认账户传0，其他他传卡项id
                equityType: save_orderData[i].manualDiscount, // 1 无权益 2折扣 3抵扣 4手动改价

                goodsId: save_orderData[i].id, // 商品id 服务，产品卡项都填写id
                itemId: save_orderData[i].itemId || 0, //取单时候传的itemId
                itemImgId: save_orderData[i].itemImgId || "0", // 预览图id
                itemName: save_orderData[i].service_name, // 商品名称
                itemType: save_orderData[i].zhonglei == 2 ? 2 : 1, // 1 服务 2产品 3卡项 4充值
                // num: save_orderData[i].zhonglei == 2 ? save_orderData[i].num : 1, // 数量，除产品外，其他都填写1
                // num调整，扣卡num会单个出现大于1的情况
                num:
                  save_orderData[i].zhonglei == 2
                    ? save_orderData[i].num
                    : save_orderData[i].num > 1
                      ? save_orderData[i].num
                      : 1,
                originPrice: Math.round(save_orderData[i].unitPrice * 100), // 充值金额  原价( 分 )
                average_price: Math.round(save_orderData[i].unitPrice * 100), // 【必填】新单价  原价( 分 )
                recharge_money: 0, // 充值金额（本金）金额 （分） 手动充值时必传
                realPay: Math.round(save_orderData[i].subtotal * 100), // 充值金额真实支付（分）
                present_money: 0, // 充值（赠送）金额 (分) 手动充值时必传
                salesmen: save_orderData[i].salesmen || [], // 选择的销售id
                skuId:
                  save_orderData[i].specifications_id ||
                  save_orderData[i].sku_val_id ||
                  0, // 规格id，非规格天写0
                skuName: save_orderData[i].sku || "", // 规格名称（如：红色,大）没有填空字符串
                stage: "1", // 当前阶段（填写1）取单会返回该字段，只有1可删除、编辑权益优惠信息
                technicians: save_orderData[i].technician_id || [], // 服务人员id
                promoterId: save_orderData[i].promoterId
                  ? save_orderData[i].promoterId
                  : 0, //推广员id
              });
            }

            let presentData = [];
            if (this.billGiftData.length > 0) {
              presentData = this.deepCopy(this.billGiftData);
              presentData.forEach((item) => {
                delete item.price;
              });
            }
            let extraData = {};
            if (this.helpStaffArr[this.isactive1]) {
              extraData["help_staff"] = this.helpStaffArr[this.isactive1].map(
                (item) => {
                  return item["id"];
                }
              );
            }
            let orderType = 1;
            //billingType：0：服务；1：产品；2：扣卡
            //orderType：1：服务；2：产品；3：办卡；4：充值 5：充卡；6 直接收款
            if (this.billingType == "1") {
              // 产品
              orderType = 2;
            } else {
              // 扣卡、服务
              orderType = 1;
            }
            $.ajax({
              url: _self.url + "/android/order/orderSave",
              type: "post",
              data: {
                // addressInfo: "", // 【可选】收货地址信息
                // bookerid: 0, // 【可选】预约id 来源是预约时使用
                buyerId: buyerId, // 用户id
                cashierId: _self.loginInfo.id, // 收银员id
                // dispatchFee: 0, // 【可选】运费 （分）
                // dispatchType: 0, // 【可选】配送类型： 1，到店自提，2，配送，0，非配送
                merchantid: _self.loginInfo.merchantid, //  商户id
                orderGiftItems: [], //订单礼品数组（预留字段）
                orderItems: JSON.stringify(orderItems), // 【必填】订单的详情信息
                // orderNo: _self.orderSourceType == 2 ? 0 : _self.orderCount || 0, // 【可选】订单号（取单时需要传）
                orderType: orderType, // 【必填】1：服务；2：产品；3：办卡；4：充值 5：充卡；6 直接收款
                promotions: [], // 【必填】预留字段（优惠信息）
                remark: _self.beizhu_info, // 订单备注
                sourceType: 1, // 来源类型 1,开单，2,预约，3,取单
                storeid: _self.loginInfo.storeid, // 店铺id
                totalPay: Math.round(_self.pay_all_show * 100), // 订单总价（分）
                shift_no: _self.loginInfo.shift_no, // 【必填】班次单号 (android接口必填)
                // couponData: 0, //现金券额外参数
                presentData: JSON.stringify(presentData), // 【可选】赠送项目
                extraData: JSON.stringify(extraData),
              },
              success: function (res) {
                try {
                  _self.goSettleLoading = false;
                  if (res && res.code == 1) {
                    // 清空协助接待信息
                    _self.helpStaffArr[_self.isactive1] = [];
                    //收款
                    //使用耗卡
                    orderItems.forEach((item) => {
                      //使用折扣卡
                      if (item.equityType == 2) {
                        if (
                          item.cardDetailsId == 0 &&
                          item.cardId == 0 &&
                          item.consumeCard == 1
                        ) {
                          _self.isPayStatus = 4;
                        } else {
                          _self.isPayStatus = 0;
                          // _self.isPayStatus = 4;
                        }
                      }
                      if (item.consumeCard == 2) {
                        //consumeCard 是否耗卡（1,不是，2，是）
                        // isPayStatus 收银台--充卡收款跳转到收银台。 0:会员余额,1:支付宝/微信,2:现金,4:自定义收款
                        _self.isPayStatus = 0;
                        // _self.isPayStatus = 4;
                      }
                    });
                    _self.buy_receipt = true;
                    _self.goSettleLoading = false;
                    _self.isCouponCard = false;
                    _self.orderNo = res.data.orderNo;
                    _self.orderId = res.data.id;
                    _self.isRechargeCard = true;
                    _self.C_open_order_specifications_save = [];
                    _self.orderSourceType = 1;
                    _self.orderCount = 0;
                    _self.couponCardCode = "";
                    _self.billGiftData = [];
                    _self.showGiftData = {
                      allPrice: 0,
                      serverNum: 0,
                      productNum: 0,
                    };
                  } else {
                    console.error("goSettle failed:", res);
                    _self.$message({
                      type: "error",
                      message: res && res.msg ? res.msg : "下单失败",
                      duration: 1500,
                    });
                    _self.goSettleLoading = false;
                  }
                  _self.deductionPrice = "";
                  _self.input_dis = "";
                } catch (error) {
                  console.error("goSettle success handler error:", error);
                  _self.goSettleLoading = false;
                }
              },
              error: function (xhr, status, error) {
                console.error("goSettle ajax error:", error, status);
                _self.goSettleLoading = false;
                if (_self.$message) {
                  _self.$message({
                    type: "error",
                    message: "网络请求失败，请重试",
                    duration: 1500,
                  });
                }
              },
            });
          } catch (error) {
            console.error("goSettle error:", error);
            this.goSettleLoading = false;
          }
        },
        binsChangeSubtotal: function (val, index) {
          val = val.replace(/[^\d\.]/g, "");
          val = val.replace(/^\./g, "");
          val = val.replace(/\.{2,}/g, ".");
          val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
          val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
          val = val.replace(/^0.$/, "0.");
          app.$set(
            this.C_open_order_specifications_save[index],
            "subtotal",
            val
          );
          this.calculatePrice();
        },
        handleUseMemberPrice(index) {
          if (
            this.memberInfo?.is_vip &&
            this.C_open_order_specifications_save[index].member_price > 0 &&
            this.C_open_order_specifications_save[index].member_price / 100 <
              this.C_open_order_specifications_save[index].price
          ) {
            const reduceAmount = (
              (this.C_open_order_specifications_save[index].price -
                this.C_open_order_specifications_save[index].member_price /
                  100) *
              this.C_open_order_specifications_save[index].num
            ).toFixed(2);
            this.C_open_order_specifications_save[index].reduceAmount =
              reduceAmount;
            this.handleReduceAmountChange(reduceAmount, index);
          }
        },
        handleReduceAmountChange(val, index) {
          val = val.replace(/[^\d\.]/g, "");
          val = val.replace(/^\./g, "");
          val = val.replace(/\.{2,}/g, ".");
          val = val.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
          val = val.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3");
          val = val.replace(/^0.$/, "0.");
          let reduceAmount = 0;
          if (val) {
            reduceAmount = val;
            // 1 无权益 2折扣 3抵扣 4手动改价
            this.C_open_order_specifications_save[index].manualDiscount = 4;
          } else {
            this.C_open_order_specifications_save[index].manualDiscount = 1;
          }
          let subtotal = (
            this.C_open_order_specifications_save[index].price *
              this.C_open_order_specifications_save[index].num -
            reduceAmount
          ).toFixed(2);
          if (subtotal < 0) {
            subtotal = 0;
            this.$set(
              this.C_open_order_specifications_save[index],
              "reduceAmount",
              (
                this.C_open_order_specifications_save[index].price *
                this.C_open_order_specifications_save[index].num
              ).toString()
            );
          } else {
            this.C_open_order_specifications_save[index].reduceAmount =
              reduceAmount;
          }
          this.C_open_order_specifications_save[index].subtotal = subtotal;
          this.calculatePrice();
        },
        getMemberStampCardInfo: function () {
          var _self = this;
          _self.loading = true;
          const data = {
            storeid: _self.loginInfo.storeid,
            merchantid: _self.loginInfo.merchantid,
            memberid: _self.memberInfo.id, //会员id
          };
          _self.sampleMemberCardInfo = [];

          $.ajax({
            url: _self.url + "/android/vip/getNewMemberStampCardInfo2",
            type: "post",
            data,
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.memberSecondaryCard = res.data;

                // 单独抽出所有权益中的卡
                res.data.forEach((item) => {
                  item.goodsInfo.forEach((goodItem) => {
                    var d = {
                      ...goodItem,
                      technician_id: [],
                      salesmen: [],
                      buyNum: 1,
                      manualDiscount: 1,
                      manualDiscountCard: {},
                      zhonglei: 3,
                      cardInfo: {
                        allnum: item.cardInfo.allnum,
                        cardSource: item.cardInfo.cardSource,
                        card_id: item.cardInfo.card_id,
                        card_name: item.cardInfo.card_name,
                        cardtype: item.cardInfo.cardtype,
                        indate: item.cardInfo.indate,
                        indateName: item.cardInfo.indateName,
                        once_cardtype: item.cardInfo.once_cardtype,
                        planMinTotal: item.cardInfo.planMinTotal,
                      },
                    };
                    _self.sampleMemberCardInfo.push(d);
                  });
                });
              } else {
                _self.memberSecondaryCard = [];
                _self.sampleMemberCardInfo = [];
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });

          _self.sampleMemberCardInfo_runOut = [];
          if (!_self.isShowLoseEfficacyCard) {
            $.ajax({
              url: _self.url + "/android/vip/getNewMemberStampCardInfo3",
              type: "post",
              data,
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  // 单独抽出所有权益中的卡
                  res.data.forEach((item) => {
                    item.goodsInfo.forEach((goodItem) => {
                      var d = {
                        ...goodItem,
                        technician_id: [],
                        salesmen: [],
                        buyNum: 1,
                        manualDiscount: 1,
                        manualDiscountCard: {},
                        zhonglei: 3,
                        cardInfo: {
                          allnum: item.cardInfo.allnum,
                          cardSource: item.cardInfo.cardSource,
                          card_id: item.cardInfo.card_id,
                          card_name: item.cardInfo.card_name,
                          cardtype: item.cardInfo.cardtype,
                          indate: item.cardInfo.indate,
                          indateName: item.cardInfo.indateName,
                          once_cardtype: item.cardInfo.once_cardtype,
                          planMinTotal: item.cardInfo.planMinTotal,
                        },
                      };
                      _self.sampleMemberCardInfo_runOut.push(d);
                    });
                  });
                }
              },
              complete: () => {
                _self.loading = false;
              },
            });
          }
        },
        // 权益次数使用
        cardUseNum2(item, item2) {
          // console.log(item,item2,'cardUseNum2');
          let num = 0;
          this.timerCardUse.forEach((b) => {
            if (
              item2["once_cardtype"] == 3 &&
              item["givenum"] == 0 &&
              b["isgive"] == 2
            ) {
              // 通卡 非赠送
              if (item2["card_id"] == b["consumeCardId"]) {
                // num += 1;
                num = num + b.num;
              }
            } else {
              // 其他次卡  需要卡项详情id和 卡项id 同时满足条件
              if (
                item2["card_id"] == b["consumeCardId"] &&
                item["card_details_id"] == b["cardDetailsId"]
              ) {
                // num += 1;
                num = num + b.num;
              }
            }
          });
          return num;
        },
        chooseGift: function () {
          this.showGiftData = {
            allPrice: 0,
            serverNum: 0,
            productNum: 0,
          };
          this.isChooseGift = true;
        },
        switchFunction(flag) {
          switch (this.billingType) {
            case "0":
              // 服务
              if (!this.cashier_open_order_service_name.length > 0 || flag) {
                this.isServerScroll = false;
                this.serviceList();
              }
              this.$nextTick(() => {
                this.$refs.cardWrap.scrollTop = 0;
                this.inputFocus(this.$refs.search_keyword);
              });
              break;
            case "1":
              // 产品
              if (!this.cashier_open_order_product_name.length > 0 || flag) {
                this.productPage = 1;
                this.isComboScroll = false;
                this.productList();
              }
              this.$nextTick(() => {
                this.$refs.cardWrap.scrollTop = 0;
                this.inputFocus(this.$refs.search_product);
              });
              break;
            case "2":
              // 扣卡
              if (!this.sampleMemberCardInfo.length > 0 || flag) {
                this.getMemberStampCardInfo();
              }
              this.$nextTick(() => {
                this.$refs.cardWrap.scrollTop = 0;
              });
              break;
          }
          this.search_product = this.search_keyword = "";
        },

        productList: function (flag) {
          var _self = this;
          _self.loading = true;
          $.ajax({
            url: _self.url + "/android/Product/getProductData",
            type: "post",
            data: {
              class: "",
              keyword: _self.search_keyword,
              label: 0,
              limit: 9999,
              merchantid: _self.loginInfo.merchantid,
              order: "1",
              page: _self.productPage,
              status: "1",
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 0) {
                // 接口上是0成功 其他都是1
                _self.productAllCount = res.count;
                if (_self.productAllCount < 10) {
                  _self.busyProduct = false;
                }
                if (flag == 1) {
                  _self.cashier_open_order_product_name =
                    _self.cashier_open_order_product_name.concat(res.data);
                  if (res.count == 0) {
                    _self.isComboScroll = true;
                  } else {
                    _self.isComboScroll = false;
                  }
                } else {
                  _self.cashier_open_order_product_name = res.data;
                }
                for (
                  let i = 0;
                  i < _self.cashier_open_order_product_name.length;
                  i++
                ) {
                  _self.cashier_open_order_product_name[i]["manualDiscount"] =
                    1;
                  _self.cashier_open_order_product_name[i][
                    "manualDiscountCard"
                  ] = {};
                  _self.cashier_open_order_product_name[i]["zhonglei"] = 2;
                  _self.cashier_open_order_product_name[i]["discount"] = 1;
                  _self.cashier_open_order_product_name[i].goodsType = 2;
                }
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
            complete: () => {
              _self.loading = false;
            },
          });
        },

        // tab 切换
        billingChangeType: function (tab, event) {
          if (this.billingType == "2" && !this.memberInfo.id) {
            // 控制<member-search-bar>isShowMemberSearch = true
            this.isShowMemberSearch = true;
          }
          this.search_keyword = "";
          this.clearPageWithOutMemberInfo();
          this.switchFunction();
        },

        // 处理价格标签的鼠标事件
        // 处理价格标签的悬停事件
        handlePriceTagHover: function (event, isEnter) {
          try {
            const serviceCard = event.target.closest(".o-service-card");
            if (serviceCard) {
              if (isEnter) {
                serviceCard.classList.add("price-tag-hovered");
              } else {
                serviceCard.classList.remove("price-tag-hovered");
              }
            }
          } catch (error) {
            console.error("handlePriceTagHover 出错:", error);
          }
        },
        //首页--获取服务人员接口-开单
        change_ji_shi: function (index, isMerge = false) {
          this.loading = false;
          if (!isMerge) {
            this.ji_shi = true;
          }
          var _self = this;
          //在公共地区定义一个变量用来确定点击选择服务人员时候是给哪个服务添加的服务人员
          _self.which_server_technician = index;

          let technicianArr =
            _self.C_open_order_specifications_save[index].technician_id;
          // _self.C_open_order_specifications_save
          _self.ji_shi_zhanshi = [];
          $.ajax({
            url: _self.url + "/android/Staff/Craftsman",
            type: "post",
            data: {
              serviceid: _self.C_open_order_specifications_save[index].id,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                let arr = [];
                res.data.forEach((item) => {
                  let flag = arr.some((i) => {
                    if (i["id"] == item["id"]) {
                      return true;
                    }
                  });
                  if (!flag) {
                    arr.push(item);
                  }
                });
                _self.ji_shis = arr;
                if (technicianArr && technicianArr.length != 0) {
                  // debugger_test
                  let obj = {};
                  for (let i = 0; i < _self.ji_shis.length; i++) {
                    for (let j = 0; j < technicianArr.length; j++) {
                      if (technicianArr[j].id == _self.ji_shis[i].id) {
                        if (technicianArr[j].dot == 1) {
                          _self.ji_shis[i]["is_choice_jishi"] = true;
                          _self.ji_shis[i]["is_guest"] = true;
                        } else {
                          _self.ji_shis[i]["is_choice_jishi"] = true;
                          _self.ji_shis[i]["is_guest"] = false;
                        }
                        obj[technicianArr[j].id] = _self.ji_shis[i];
                      }
                    }
                    if (_self.ji_shis[i].hasOwnProperty("is_choice_jishi")) {
                    } else {
                      _self.ji_shis[i]["is_choice_jishi"] = false;
                      _self.ji_shis[i]["is_guest"] = false;
                    }
                  }
                  technicianArr.forEach((item) => {
                    _self.ji_shi_zhanshi.push(obj[item.id]);
                  });
                } else {
                  for (let i = 0; i < _self.ji_shis.length; i++) {
                    _self.ji_shis[i]["is_choice_jishi"] = false;
                    _self.ji_shis[i]["is_guest"] = false;
                  }
                }

                // _self.ji_shi_zhanshi=[];
                // console.log("服务人员信息服务人员信息")
                // console.log(_self.ji_shis);
                _self.loading = false;
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },
        //开单--选择销售
        change_xiaoshou: function (index, isMerge = false) {
          this.loading = false;
          if (!isMerge) {
            this.xiao_shou = true;
          }
          var _self = this;
          let salemenArr =
            _self.C_open_order_specifications_save[index].salesmen;
          _self.xiao_shou_zhanshi = [];
          _self.which_server_Sale = index;
          $.ajax({
            url: _self.url + "/android/Staff/sellsman",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                // console.log(res);
                _self.xiaoshous = res["data"];
                if (salemenArr?.length != 0) {
                  let obj = {};
                  for (let i = 0; i < _self.xiaoshous.length; i++) {
                    let flag = true;
                    for (let j = 0; j < salemenArr.length; j++) {
                      if (salemenArr[j] == _self.xiaoshous[i].id) {
                        _self.xiaoshous[i]["is_choice_xiaoshou"] = true;
                        flag = false;
                        obj[salemenArr[j]] = _self.xiaoshous[i];
                        break;
                      }
                    }
                    if (flag) {
                      _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
                    }
                  }
                  salemenArr.forEach((item) => {
                    _self.xiao_shou_zhanshi.push(obj[item]);
                  });
                } else {
                  for (let i = 0; i < _self.xiaoshous.length; i++) {
                    _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
                  }
                }
                // console.log(_self.xiaoshous);
                _self.loading = false;
              } else {
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },
        //确认服务人员
        //ji_shi_zhanshi_name是存入C_open_order_specifications_save的key预存内容，有展示的服务人员的name组合（分顺序）
        open_save_technician: function () {
          // console.log("确认服务人员信息")
          let _self = this;
          _self.ji_shi = false;
          let index = _self.which_server_technician; //index是哪一个服务的替换值
          let ji_shi_zhanshi_name = "";
          let technicianArr = [];
          for (let i = 0; i < _self.ji_shi_zhanshi.length; i++) {
            let item = {};
            if (_self.ji_shi_zhanshi[i]["is_guest"] == true) {
              item["id"] = _self.ji_shi_zhanshi[i]["id"];
              item["nickname"] = _self.ji_shi_zhanshi[i]["nickname"];
              item["dot"] = 1;
            } else {
              item["id"] = _self.ji_shi_zhanshi[i]["id"];
              item["nickname"] = _self.ji_shi_zhanshi[i]["nickname"];
              item["dot"] = 0;
            }
            ji_shi_zhanshi_name += item["nickname"] + "、";
            technicianArr.push(item);
          }
          ji_shi_zhanshi_name = ji_shi_zhanshi_name.slice(0, -1);
          _self.pi_is_ji_name = ji_shi_zhanshi_name;
          _self.C_open_order_specifications_save[index]["technician_id"] =
            technicianArr;
          _self.C_open_order_specifications_save[index]["technician_name"] =
            ji_shi_zhanshi_name;
          _self.ji_shi_zhanshi = [];
          _self.ji_shi_save = [];
        },
        //取消确定的销售
        xiaoshou_over: function () {
          let _self = this;
          _self.xiao_shou = false;
          _self.xiao_shou_zhanshi = [];
          _self.xiao_shou_save = [];
          for (let i = 0; i < _self.xiaoshous.length; i++) {
            _self.xiaoshous[i]["is_choice_xiaoshou"] = false;
          }
          _self.$forceUpdate();
        },
        //确定销售
        xiaoshou_save: function () {
          let _self = this;
          let index = _self.which_server_Sale; //index是哪一个服务的替换值
          let xiao_shou_zhanshi_name = "";
          for (let i = 0; i < _self.xiao_shou_zhanshi.length; i++) {
            if (i == _self.xiao_shou_zhanshi.length - 1) {
              xiao_shou_zhanshi_name += _self.xiao_shou_zhanshi[i]["nickname"];
            } else {
              xiao_shou_zhanshi_name +=
                _self.xiao_shou_zhanshi[i]["nickname"] + "、";
            }
          }
          _self.pi_is_xiao_name = xiao_shou_zhanshi_name;
          //销售拼接
          _self.C_open_order_specifications_save[index]["saler_name"] =
            xiao_shou_zhanshi_name;
          // _self.xiao_shou_save[i] = [];
          for (let i = 0; i < _self.xiao_shou_zhanshi.length; i++) {
            if (_self.xiao_shou_zhanshi[i]["is_choice_xiaoshou"] == true) {
              _self.xiao_shou_save.push(_self.xiao_shou_zhanshi[i]["id"]);
            }
          }
          _self.C_open_order_specifications_save[index]["salesmen"] =
            _self.xiao_shou_save;
          this.xiaoshou_over();
        },
        //取消确认服务人员
        open_over_technician: function () {
          let _self = this;
          _self.ji_shi = false;
          _self.ji_shi_zhanshi = [];
          _self.ji_shi_save = [];
          for (let i = 0; i < _self.ji_shis.length; i++) {
            _self.ji_shis[i]["is_choice_jishi"] = false;
            _self.ji_shis[i]["is_guest"] = false;
          }
          _self.$forceUpdate();
        },
        //提成合并选择
        handleMergeSelect: function (index) {
          this.change_ji_shi(index, true);
          this.change_xiaoshou(index, true);
          this.isShowMergeDialog = true;
        },
        //合并选择提交
        handleMergeSubmit: function () {
          this.xiaoshou_save();
          this.open_save_technician();
          this.isShowMergeDialog = false;
        },
        handleCannelMerge: function () {
          this.xiaoshou_over();
          this.open_over_technician();
          this.isShowMergeDialog = false;
        },
        //选择服务人员的点击事件  ji_shi_id用来判断从保存技术数组中删除服务人员的值
        //ji_shi_zhanshi数组用来存放在选择服务人员时候被选中的服务人员信息，按照点击的顺序来排序
        chioce_ji_shi_name: function (index, data, ji_shi_id) {
          let _self = this;
          let item = _self.ji_shis[index];
          if (data) {
            // 选中
            _self.ji_shi_zhanshi.push(item);
          } else {
            // 取消
            _self.ji_shi_zhanshi.some((it, j) => {
              if (it["id"] == ji_shi_id) {
                _self.ji_shi_zhanshi.splice(j, 1);
                return true;
              }
            });
          }
          _self.$forceUpdate();
        },
        //选择点客的点击事件ji_shi_id用来判断
        chioce_ji_shi_guest: function (index, data, ji_shi_id) {
          let _self = this;
          for (let i = 0; i < _self.ji_shis.length; i++) {
            if (index == i) {
              if (_self.ji_shis[i]["is_choice_jishi"] == false) {
                _self.ji_shis[i]["is_choice_jishi"] = data;
                _self.ji_shis[i]["is_guest"] = data;
                _self.ji_shi_zhanshi.push(_self.ji_shis[i]);
              } else {
                for (let j = 0; j < _self.ji_shi_zhanshi.length; j++) {
                  if (_self.ji_shi_zhanshi[j]["id"] == ji_shi_id) {
                    _self.ji_shi_zhanshi[j]["is_guest"] = data;
                  }
                }
              }
            }
          }
          _self.ji_shi_zhanshi.some((it, j) => {
            if (it["id"] == ji_shi_id) {
              _self.ji_shi_zhanshi[j]["is_guest"] = data;
              return true;
            }
          });
          _self.$forceUpdate();
        },
        /* setupGlobalMouseEvents: function () {
          try {
            const _self = this;
            // 全局鼠标松开事件，确保长按能正确结束
            _self.globalMouseUpHandler = function (event) {
              try {
                if (event && typeof event.button === "number") {
                  if (event.button === 0) {
                    _self.handleGlobalLeftMouseUp(event);
                  } else if (event.button === 2) {
                    _self.handleGlobalRightMouseUp(event);
                  }
                }
              } catch (handlerError) {
                console.error("globalMouseUpHandler 出错:", handlerError);
              }
            };

            if (document && document.addEventListener) {
              document.addEventListener("mouseup", _self.globalMouseUpHandler);
            }
          } catch (error) {
            console.error("setupGlobalMouseEvents 方法出错:", error);
          }
        }, */
        handleMemberSelect: function (memberInfo) {
          this.memberInfo = memberInfo;
          this.billingType = "2";
          this.getMemberStampCardInfo();
        },
        showChooseGift: function () {
          this.isChooseGift = false;
        },
        chooseGiftData: function (data) {
          this.billGiftData = data;
          if (this.billGiftData.length != 0) {
            for (let i = 0; i < this.billGiftData.length; i++) {
              // debugger_test
              let item = this.billGiftData[i];
              if (item.itemType == 1) {
                this.showGiftData.serverNum =
                  this.showGiftData.serverNum + Number(item.num);
              } else {
                this.showGiftData.productNum =
                  this.showGiftData.productNum + Number(item.num);
              }
              this.showGiftData.allPrice =
                this.showGiftData.allPrice + Number(item.originPrice);
            }
          }
        },
        //调整单价
        handleUnitPriceChange: function (index) {
          const _self = this;
          const item = _self.C_open_order_specifications_save[index];
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const quantity = parseInt(item.num) || 1;

          // 计算小计 = 单价 * 数量
          const subtotal = (unitPrice * quantity).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * quantity * 100);

          // 重新计算总价
          _self.calculatePrice();
        },

        //设置单价
        handleSetUnitPrice: function (index, price) {
          const item = this.C_open_order_specifications_save[index];
          const unitPrice = parseFloat(price) || 0;
          const quantity = parseInt(item.num) || 1;

          // 设置单价
          item.unitPrice = unitPrice.toFixed(2);

          // 计算小计 = 单价 * 数量
          const subtotal = (unitPrice * quantity).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * quantity * 100);

          // 重新计算总价
          this.calculatePrice();

          // 触发单价输入框动画效果
          this.$nextTick(() => {
            const serviceCards = this.getServiceCards();
            if (serviceCards[index]) {
              const priceInput = serviceCards[index].querySelector(
                ".o-price-select-hover"
              );
              if (priceInput) {
                priceInput.classList.add("o-price-update-animation");
                this.timerManager.createTimer(
                  "priceAnimation_" + index,
                  () => {
                    priceInput.classList.remove("o-price-update-animation");
                  },
                  600
                );
              }
            }
          });
        },
        closeHelpStaffVisible(type) {
          // 1, 确定关闭 2，取消关闭
          if (type) {
            this.helpStaffArr[this.isactive1] = [...this.checkHelpStaffArr];
          }
          this.checkHelpStaffArr = [];
          this.helpStaffVisible = false;
        },
        //聚焦
        inputFocus: function (dom) {
          this.$nextTick(
            function () {
              try {
                dom.focus();
              } catch (e) {}
            }.bind(this)
          );
        },
        clearPageWithOutMemberInfo() {
          this.C_open_order_specifications_save = [];
          this.beizhu_info = "";
          this.zhk_xiao_shou_zhanshi = [];
          this.xiao_shou_zhanshi = [];
          this.SalesShow = "";
          this.pay_all_show = "0.00";
          this.pay_all = 0;
          this.helpStaffArr[this.isactive1] = [];
          this.cardinfo = {
            permanent: "2",
            validity_time: "",
            effectiveMethod: "1",
            dueDate: "",
          };

          // 重置服务卡片的动画状态
          this.cashier_open_order_service_name.forEach((item) => {
            this.$set(item, "_justAdded", false);
          });

          this.inputFocus(this.$refs.search_keyword);
        },
        //clearPage 清空页面
        clearPage: function () {
          this.clearPageWithOutMemberInfo();
          this.memberInfo = {};
          this.sampleMemberCardInfo = [];
        },
        //关闭收款
        bindClosePay: function (flag) {
          try {
            this.buy_receipt = flag;
            // 调用 member-search-bar 组件的 clearMemberInfo 方法
            if (this.$refs.memberSearchBar) {
              this.$refs.memberSearchBar.clearMemberInfo();
            }
            // this.clearPage();
          } catch (error) {
            console.error("bindClosePay error:", error);
          }
        },
        //选择销售弹框
        zhkchange_xiaoshou: function () {
          this.zhk_xiao_shou = true;
          this.loading = true;
          let salemenArr = this.zhk_xiao_shou_zhanshi;
          this.xiao_shou_zhanshi = salemenArr;
          $.ajax({
            url: this.url + "/android/Staff/sellsman",
            type: "post",
            data: {
              merchantid: this.loginInfo.merchantid,
              storeid: this.loginInfo.storeid,
            },
            success: (res) => {
              if (res.code == 1) {
                this.xiaoshous = res.data;
                if (salemenArr?.length != 0) {
                  for (let i = 0; i < this.xiaoshous.length; i++) {
                    let flag = true;
                    for (let j = 0; j < salemenArr.length; j++) {
                      if (salemenArr[j].id == this.xiaoshous[i].id) {
                        this.xiaoshous[i]["is_choice_xiaoshou"] = true;
                        flag = false;
                        break;
                      }
                    }
                    if (flag) {
                      this.xiaoshous[i]["is_choice_xiaoshou"] = false;
                    }
                  }
                } else {
                  for (let i = 0; i < this.xiaoshous.length; i++) {
                    this.xiaoshous[i]["is_choice_xiaoshou"] = false;
                  }
                }
                this.loading = false;
              } else {
                this.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        //选择销售框
        chioce_xiaoshou: function (index, data, xiao_shou_id) {
          let item = this.xiaoshous[index];
          if (data == true) {
            item["is_choice_xiaoshou"] = data;
            this.xiao_shou_zhanshi.push(item);
          } else {
            for (let j = 0; j < this.xiao_shou_zhanshi.length; j++) {
              if (this.xiao_shou_zhanshi[j]["id"] == xiao_shou_id) {
                this.xiao_shou_zhanshi.splice(j, 1);
              }
            }
          }
          this.$forceUpdate();
        },

        //删除服务
        zhk_open_details_price_del: function (index) {
          // 获取要删除的服务信息用于提示
          var deletedService = this.C_open_order_specifications_save[index];

          // 添加删除动画类
          this.$set(deletedService, "_removing", true);

          // 延迟删除以显示缩小动画
          this.timerManager.createTimer(
            "deleteService_" + index,
            () => {
              // 删除服务项
              this.C_open_order_specifications_save.splice(index, 1);
              this.calculatePrice();
            },
            100
          );
        },

        //自动查询服务
        serviceList: function (flag) {
          this.loading = true;
          $.ajax({
            url: this.url + "/android/Service/serviceList",
            type: "post",
            data: {
              keyword: this.search_keyword,
              labelid: this.serverLabelid,
              sort: 1, // 默认正序，1正序 2倒序
              status: 1, // 1上架。  2下架
              storeid: this.loginInfo.storeid,
              page: 1,
              limit: 99999,
            },
            success: (res) => {
              this.loading = false;
              if (res.code == 0) {
                this.serverAllCount = res.count;
                if (this.serverAllCount < 10) {
                  this.busy = false;
                }
                if (flag == 1) {
                  this.cashier_open_order_service_name =
                    this.cashier_open_order_service_name.concat(res.data);
                  if (res.count == 0) {
                    this.isServerScroll = true;
                  } else {
                    this.isServerScroll = false;
                  }
                } else {
                  this.cashier_open_order_service_name = res.data;
                  this.isServerScroll = false;
                }
                //获取数据后然后对数据赋值这个时候将查不到数据的div属性换掉
                //以下在加载服务时候给没一个服务添加一个服务人员id用来存储后面要穿的值
                for (
                  let i = 0;
                  i < this.cashier_open_order_service_name.length;
                  i++
                ) {
                  this.cashier_open_order_service_name[i]["technician_id"] = [];
                  this.cashier_open_order_service_name[i]["salesmen"] = [];
                  this.cashier_open_order_service_name[i]["num"] = 1;
                  this.cashier_open_order_service_name[i]["manualDiscount"] = 1;
                  this.cashier_open_order_service_name[i]["discount"] = 1;
                  this.cashier_open_order_service_name[i][
                    "manualDiscountCard"
                  ] = {};
                  this.cashier_open_order_service_name[i]["zhonglei"] = 1; //添加一个字段用来判断种类zhonglei: 1 服务 2产品 3卡项 4充值
                  this.cashier_open_order_service_name[i]["_animationId"] =
                    Date.now() + i; //添加动画标识
                }
              } else {
                this.cashier_open_order_service_name = [];
                this.$message({
                  type: "error",
                  message: "暂无数据",
                  duration: 1500,
                });
              }
            },
            complete: () => {
              this.loading = false;
            },
          });
        },
        //选择服务
        bind_add_server: function (data, index) {
          // debugger;
          this.data_server_product = this.deepCopy(data);
          this.C_open_order_Specifications =
            this.cashier_open_order_service_name[index];
          this.cashier_open_order_service_choice[0] =
            this.cashier_open_order_service_name[index];

          if (data.status == 1) {
            this.cashier_open_order_service_choice[0]["specifications_id"] = 0;
            let newObj = this.deepCopy(
              this.cashier_open_order_service_choice[0]
            );
            /* 会员价开始 */
            let memberPriceData = [];
            let levelId = 0;
            let price = newObj.price;
            if (
              this.memberInfo &&
              this.memberInfo.levelInfo &&
              this.memberInfo.levelInfo.id
            ) {
              levelId = this.memberInfo.levelInfo.id;
            }
            if (data["memberPriceData"]) {
              memberPriceData = data["memberPriceData"];
            }
            memberPriceData.some((item) => {
              if (item["sku_id"] == 0 && item["member_level_id"] == levelId) {
                price = parseFloat((item["price"] / 100).toFixed(2));
                return true;
              }
              return false;
            });
            newObj.price = price;
            newObj["unitPrice"] = price;
            /* 会员价结束 */

            newObj["subtotal"] = (newObj.num * newObj.unitPrice).toFixed(2);
            newObj["isActiveCostCard"] = 1;
            newObj["judgeCard"] = 0;
            newObj["totalAmount"] = price * newObj.num * 100;
            newObj["subtotal"] = (price * newObj.num).toFixed(2);
            newObj["_animationId"] = Date.now() + Math.random();

            this.$set(data, "_justAdded", true);
            this.timerManager.createTimer(
              "justAdded_" + data.id,
              () => {
                this.$set(data, "_justAdded", false);
              },
              600
            );

            this.C_open_order_specifications_save.unshift(
              this.deepCopy(newObj)
            );
            // 滚动到顶端
            this.$nextTick(() => {
              if (this.$refs.rightScrollContainer) {
                this.$refs.rightScrollContainer.scrollTo({
                  top: 0,
                  behavior: "smooth",
                });
              }
            });
            if (this.C_open_order_specifications_save.judgeCard == 3) {
              this.C_open_order_specifications_save.couponCard = {};
              this.isCouponCard = false;
              this.C_open_order_specifications_save.judgeCard = 0;
              this.assignGoods = false;
            }

            // this.handleUseMemberPrice(0);
            this.calculatePrice();
          } else {
            this.$message({
              message: "该商品已经下架",
              center: true,
              duration: 1500,
            });
            this.loading = false;
          }
        },

        //产品接口添加到开单详情在这里面为产品的字段更改和添加，注意尽量统一一级添加的位置（此处就是）
        //在此判断是否有规格  有则请求数据接口
        bind_add_product: function (data, index) {
          // debugger;
          this.data_server_product = this.deepCopy(data);
          this.cashier_open_order_service_choice[0] =
            this.cashier_open_order_product_name[index];

          let newObj = new Object();
          let is_repeat = false;
          //创建临时的值用来替换接口中的key
          let id = data["id"].toString();
          this.C_open_order_specifications_name = []; //将要循环的数值和对比的清空
          this.C_open_order_specifications_attr = [];
          var _self = this;
          // console.log(this.C_open_order_specifications_save);
          // console.log(id);
          //以下将名字  价格  服务人员  服务人员展示销售 销售展示等其他数据重新进行深拷贝复制

          newObj["id"] = data["id"];
          newObj["product_id"] = data["product_id"];
          newObj["imgarr"] = data["imgarr"];
          newObj["issku"] = data["issku"];
          if (data["issku"] == 1) {
            newObj["realPrice"] = (data["price"] / 100).toFixed(2);
            newObj["price"] = data["realPrice"];
            if (data["memberPriceData"]) {
              newObj["memberPriceData"] = data["memberPriceData"];
            }
          } else {
            /* 会员价开始 */
            let memberPriceData = [];
            let levelId = 0;
            let price = (data["price"] / 100).toFixed(2);
            if (
              _self.memberInfo &&
              _self.memberInfo.levelInfo &&
              _self.memberInfo.levelInfo.id
            ) {
              levelId = _self.memberInfo.levelInfo.id;
            }
            if (data["memberPriceData"]) {
              memberPriceData = data["memberPriceData"];
            }
            memberPriceData.some((item) => {
              if (item["sku_id"] == 0 && item["member_level_id"] == levelId) {
                price = parseFloat((item["price"] / 100).toFixed(2));

                return true;
              }
              return false;
            });
            /* 会员价结束 */
            newObj["realPrice"] = price;
            newObj["price"] = price;
            newObj["unitPrice"] = price;
          }
          newObj["member_price"] = data["member_price"];
          newObj["itemImgId"] = data["itemImgId"];
          newObj["totalnum"] = data["totalnum"];
          newObj["subtotal"] = newObj["unitPrice"] * 1;
          newObj["totalAmount"] = newObj["subtotal"] * 100;
          newObj["totalsell"] = data["totalsell"];
          newObj["service_name"] = data["product_name"];
          newObj["unit"] = data["unit"];
          newObj["salesmen"] = [];
          newObj["technician_id"] = [];
          newObj["saler_name"] = "";
          newObj["technician_name"] = "";
          newObj["num"] = 1;
          newObj["sku_val_id"] = "";
          newObj["zhonglei"] = 2;
          newObj["manualDiscount"] = data["manualDiscount"];
          newObj["manualDiscountCard"] = data["manualDiscountCard"];
          newObj["_animationId"] = Date.now() + Math.random();

          this.C_open_order_Specifications = this.deepCopy(newObj);
          this.C_open_order_Specifications["imgurl"] =
            this.cashier_open_order_product_name[index]["imgarr"][0];
          // console.log(this.C_open_order_Specifications);
          if (data["issku"] == 1) {
            // bind_add_product
            this.loading = true;
            _self.cashier_open_order_Specifications = true;
            $.ajax({
              url: _self.url + "/android/Product/getProductInfo",
              type: "post",
              data: {
                productId: id,
                merchantid: _self.loginInfo.merchantid,
                storeid: _self.loginInfo.storeid,
              },
              success: function (res) {
                // var res = JSON.parse(res);
                if (res.code == 1) {
                  _self.C_open_order_specifications_name =
                    res["data"]["skuinfo"]["skuattr"];
                  _self.C_open_order_specifications_attr =
                    res["data"]["skuinfo"]["skulist"];
                  for (
                    let i = 0;
                    i < _self.C_open_order_specifications_name.length;
                    i++
                  ) {
                    for (
                      let j = 0;
                      j <
                      _self.C_open_order_specifications_name[i]["item"].length;
                      j++
                    ) {
                      _self.C_open_order_specifications_name[i]["item"][
                        j
                      ].is_show = false;
                    }
                  }
                  _self.loading = false;
                } else {
                  // console.log(1);
                  _self.$message({
                    type: "error",
                    message: res.msg,
                    duration: 1500,
                  });
                }
              },
              complete: () => {
                _self.loading = false;
              },
            });
          } else {
            newObj["is_open_product_jishi"] = true;
            newObj["isActiveCostCard"] = 1;
            newObj["judgeCard"] = 0;
            // console.log('no');
            if (this.C_open_order_specifications_save.length > 0) {
              // 查找是否已存在该服务
              let duplicateIndex = -1;

              let productMerge = false; // 默认不能合并
              _self.C_open_order_specifications_save.some((item, i) => {
                if (
                  item["id"] == id &&
                  item["zhonglei"] == 2 &&
                  item["issku"] == 2
                ) {
                  // 判断是否可以合并  非规格产品
                  // item['manualDiscount'] 1 无权益 2折扣 3抵扣 4手动改价 0 无权益
                  if (
                    item["manualDiscount"] == 1 &&
                    item["judgeCard"] == 0 &&
                    item["isActiveCostCard"] == 1
                  ) {
                    // 购物车中相同产品未使用权益 --- 能合并
                    this.C_open_order_specifications_save[i]["duplicateIndex"] =
                      i;
                    this.C_open_order_specifications_save[i]["num"] += 1;
                    this.C_open_order_specifications_save[i]["subtotal"] = (
                      this.C_open_order_specifications_save[i]["num"] *
                      this.C_open_order_specifications_save[i]["unitPrice"]
                    ).toFixed(2);
                    this.C_open_order_specifications_save[i]["totalAmount"] =
                      this.C_open_order_specifications_save[i]["subtotal"] *
                      100;
                    productMerge = true;
                    duplicateIndex = i;

                    return true;
                  } else {
                    // 购物车中相同产品已使用权益 --- 不能合并
                  }
                }
              });
              // 购物车已有产品未找到能合并的
              if (!productMerge) {
                this.C_open_order_specifications_save.unshift(
                  this.deepCopy(newObj)
                );
                // 滚动到顶端
                this.$nextTick(() => {
                  if (this.$refs.rightScrollContainer) {
                    this.$refs.rightScrollContainer.scrollTo({
                      top: 0,
                      behavior: "smooth",
                    });
                  }
                });
              } else {
                // 滚动到对应项并高亮
                this.$nextTick(() => {
                  const serviceCards = this.getServiceCards();
                  if (serviceCards[duplicateIndex]) {
                    serviceCards[duplicateIndex].scrollIntoView({
                      behavior: "smooth",
                      block: "center",
                    });

                    serviceCards[duplicateIndex].classList.add(
                      "o-service-card-highlight"
                    );
                    this.timerManager.createTimer(
                      "highlight_" + duplicateIndex,
                      () => {
                        serviceCards[duplicateIndex].classList.remove(
                          "o-service-card-highlight"
                        );
                      },
                      1000
                    );
                  }
                });
              }
              this.$set(data, "_justAdded", true);
              this.timerManager.createTimer(
                "justAdded_" + data.id,
                () => {
                  this.$set(data, "_justAdded", false);
                },
                600
              );
            } else {
              this.$set(data, "_justAdded", true);
              this.timerManager.createTimer(
                "justAdded_" + data.id,
                () => {
                  this.$set(data, "_justAdded", false);
                },
                600
              );
              this.C_open_order_specifications_save.unshift(
                this.deepCopy(newObj)
              );
              // 滚动到顶端
              this.$nextTick(() => {
                if (this.$refs.rightScrollContainer) {
                  this.$refs.rightScrollContainer.scrollTo({
                    top: 0,
                    behavior: "smooth",
                  });
                }
              });
            }
            //添加产品时，优惠券信息清空
            if (this.C_open_order_specifications_save.judgeCard == 3) {
              this.C_open_order_specifications_save.couponCard = {};
              this.isCouponCard = false;
              this.C_open_order_specifications_save.judgeCard = 0;
              _self.assignGoods = false;
            }
            // this.handleUseMemberPrice(0);
            // this.Pending();
            this.calculatePrice();
            _self.$forceUpdate();
          }
        },
        //选择扣卡   会员权益，扣卡
        bind_add_membercard: function (data, cardInfo) {
          let _self = this;
          // debugger;
          data = this.deepCopy(data);
          if (typeof data.can_use !== "undefined" && data.can_use == 0) {
            return this.$message.warning("权益未到可用期限");
          }
          // 是否能使用会员价
          /* 会员价开始 */
          if (data.issku == 2) {
            // 非规格数据
            let memberPriceData = [];
            let levelId = 0;
            // data['price'] 单位是元
            let price = (data["price"] * 1).toFixed(2);
            if (
              _self.memberInfo &&
              _self.memberInfo.levelInfo &&
              _self.memberInfo.levelInfo.id
            ) {
              levelId = _self.memberInfo.levelInfo.id;
            }
            if (data["memberPriceData"]) {
              memberPriceData = data["memberPriceData"];
            }
            memberPriceData.some((item) => {
              if (item["sku_id"] == 0 && item["member_level_id"] == levelId) {
                price = parseFloat((item["price"] / 100).toFixed(2));
                return true;
              }
              return false;
            });
            data["realPrice"] = price;
            data["price"] = price;
          }
          /* 会员价结束 */
          cardInfo = this.deepCopy(cardInfo);
          // 先检查 余额是否充足，次数是否足够使用
          // 不能被点过去的情况
          // 1. 已下架   非次数权益
          if (data.status != 1 && data.equityType == 2) {
            return this.$message({
              message: "该商品已下架，请重新选择",
              center: true,
              duration: 1500,
            });
          }
          if (data.goods_type == 2 && data.equityType == 2) {
            // 2 产品 1 服务
            return this.$message({
              message: "产品扣卡，不支持折扣",
              center: true,
              duration: 1500,
            });
          }
          let balanceCardItem = {};
          //  data.equityType  权益类型 1 无权益 2折扣 3抵扣 4手动改价
          if (data.equityType == 2) {
            // 折扣权益  检查余额是否充足
            if (data.issku == 2) {
              if (cardInfo.cardSource < 0) {
                // 导入权益
              } else {
                let cardId = cardInfo["card_id"];
                let cardDetailsId = data["card_details_id"];
                let hasUsedMoney = 0;
                let needMoney = (
                  (data.price * 100 * data.discount) /
                  10
                ).toFixed(0);
                let balance = 0;
                this.balanceCard.some((item) => {
                  if (item["id"] == cardId) {
                    balance = item["residuebalance"];
                    balanceCardItem = item;
                    return true;
                  }
                });
                this.balanceCardUse.forEach((item) => {
                  // 耗卡id 等于当前id
                  if (item["consumeCardId"] == cardId) {
                    // console.log(item);
                    hasUsedMoney += item["money"];
                  }
                });
                if (!(balance - hasUsedMoney > needMoney)) {
                  return this.$message({
                    message: "充值卡余额不足",
                    center: true,
                    duration: 1500,
                  });
                }
              }
            }
          } else if (data.equityType == 3) {
            // 抵扣权益  检查次数是否充足
            // 是否是赠送
            let cardId = cardInfo["card_id"];
            let cardDetailsId = data["card_details_id"];
            let hasUsedNum = 0;
            // 统计已使用次数
            this.timerCardUse.forEach((item) => {
              if (cardInfo["once_cardtype"] == 3 && !(data.givenum > 0)) {
                // 通卡，非赠送
                if (item["cardId"] == cardId && item["once_cardtype"] == 3) {
                  hasUsedNum = hasUsedNum + item["num"];
                }
              } else {
                if (
                  item["cardId"] == cardId &&
                  item["cardDetailsId"] == cardDetailsId
                ) {
                  hasUsedNum = hasUsedNum + item["num"];
                }
              }
            });
            if (data.givenum > 0 && hasUsedNum >= data.givenum) {
              // 是赠送
              return this.$message({
                type: "error",
                message: "可用次数不足",
                center: true,
                duration: 1500,
              });
            } else {
              if (data.infinite == 1) {
                // 无限次数
              } else {
                if (hasUsedNum >= data.num) {
                  return this.$message({
                    type: "error",
                    message: "可用次数不足",
                    center: true,
                    duration: 1500,
                  });
                }
              }
            }
          } else {
            return this.$message({
              message: "扣卡一定使用权益，数据错误",
              center: true,
              duration: 1500,
            });
          }
          _self.is_product_server = false;
          _self.data_server_product = data;
          data.membercardChangeIndex = this.membercardChangeIndex; //把当前折叠面板的index值存到数组中去
          let data1 = data;
          this.C_open_order_Specifications = data;
          this.C_open_order_Specifications["imgurl"] = data["img"];
          this.cashier_open_order_service_choice[0] = data;
          if (data1.issku == 1) {
            // 规格数据
            if (
              typeof data.goods_type !== "undefined" &&
              data.goods_type == 2
            ) {
              // 扣卡规格产品  bind_add_product
              let id = data1.id;
              _self.loading = true;
              // 产品 规格选择
              this.is_product_server = true;

              _self.chooseProjuctFlag = true;
              // 保存 扣卡数据 handleCloseSpecification
              _self.chooseProjuctService = {
                id: id,
                cardId: cardInfo.id,
                givenum: data.givenum,
                data: data,
                cardInfo: cardInfo,
              };
              this.C_open_order_Specifications = JSON.parse(
                JSON.stringify(data1)
              );
              _self.cashier_open_order_Specifications = true;
              this.C_open_order_specifications_name = []; //将要循环的数值和对比的清空
              this.C_open_order_specifications_attr = [];
              $.ajax({
                url: _self.url + "/android/Product/getProductInfo",
                type: "post",
                data: {
                  productId: id,
                  merchantid: _self.loginInfo.merchantid,
                  storeid: _self.loginInfo.storeid,
                },
                success: function (res) {
                  // var res = JSON.parse(res);
                  if (res.code == 1) {
                    _self.C_open_order_specifications_name =
                      res["data"]["skuinfo"]["skuattr"];
                    _self.C_open_order_specifications_attr =
                      res["data"]["skuinfo"]["skulist"];
                    for (
                      let i = 0;
                      i < _self.C_open_order_specifications_name.length;
                      i++
                    ) {
                      for (
                        let j = 0;
                        j <
                        _self.C_open_order_specifications_name[i]["item"]
                          .length;
                        j++
                      ) {
                        _self.C_open_order_specifications_name[i]["item"][
                          j
                        ].is_show = false;
                      }
                    }
                  } else {
                    // console.log(1);
                    _self.$message({
                      type: "error",
                      message: res.msg,
                      duration: 1500,
                    });
                  }
                },
                complete: () => {
                  _self.loading = false;
                },
              });
            } else {
              this.C_open_order_Specifications = JSON.parse(
                JSON.stringify(data1)
              );
              this.cashier_open_order_Specifications = true;
              let id = data1.id;
              _self.loading = true;
              _self.chooseProjuctFlag = true;
              // 保存 扣卡数据
              _self.chooseProjuctService = {
                id: id,
                cardId: cardInfo.id,
                givenum: data.givenum,
                data: data,
                cardInfo: cardInfo,
              };
              // 获取规格数据
              $.ajax({
                url: _self.url + "/android/Service/serviceSku",
                type: "post",
                data: {
                  merchantid: _self.loginInfo.merchantid,
                  serviceid: id,
                  storeid: _self.loginInfo.storeid,
                },
                success: function (res) {
                  // var res = JSON.parse(res);
                  if (res.code == 1) {
                    // console.log(res);
                    _self.C_open_order_specifications_name = res.data["sku"];
                    _self.C_open_order_specifications_attr =
                      res.data["sku_attr"];
                    for (
                      let i = 0;
                      i < _self.C_open_order_specifications_name.length;
                      i++
                    ) {
                      for (
                        let j = 0;
                        j <
                        _self.C_open_order_specifications_name[i]["sku_val"]
                          .length;
                        j++
                      ) {
                        _self.C_open_order_specifications_name[i]["sku_val"][
                          j
                        ].is_show = false;
                      }
                    }
                    _self.serviceSpecificationStatus = data["status"];
                  } else {
                    _self.$message({
                      type: "error",
                      message: res.msg,
                      duration: 1500,
                    });
                  }
                },
                complete: () => {
                  _self.loading = false;
                },
              });
            }
          } else {
            // 添加进购物车
            //  data.equityType  权益类型 1 无权益 2折扣 3抵扣 4手动改价
            if (data.equityType == 2) {
              // 导入权益
              if (cardInfo.cardSource < 0) {
                item = {};
                item.discount = data.discount;
                item.cardName = cardInfo.card_name;
                item.membercard_id = cardInfo.card_id;
                item.cardDetailsId = data.card_details_id;
                item.cardSource = cardInfo.cardSource;
                let newObj = data;
                // item.id = item.card_id;
                newObj["specifications_id"] = 0;
                newObj["manualDiscount"] = data.equityTypes;
                newObj["manualDiscountCard"] = item;
                newObj["zhonglei"] = 3;
                newObj["discount"] = data.discount;
                newObj["choosemembercardId"] = data.card_details_id;
                newObj["chooseMemberCardCount"] = 0;
                newObj["isActiveCostCard"] = 1;
                newObj.judgeCard = 0;
                newObj.costCard = {};
                newObj.costCard.card_info = "不使用耗卡";
                newObj["subtotal"] = data.buyNum * data.price;
                newObj["_animationId"] = Date.now() + Math.random();
                this.$set(data, "_justAdded", true);
                this.timerManager.createTimer(
                  "justAdded_" + data.id,
                  () => {
                    this.$set(data, "_justAdded", false);
                  },
                  600
                );
                _self.C_open_order_specifications_save.unshift(
                  this.deepCopy(newObj)
                );
                // 滚动到顶端
                this.$nextTick(() => {
                  if (_self.$refs.rightScrollContainer) {
                    _self.$refs.rightScrollContainer.scrollTo({
                      top: 0,
                      behavior: "smooth",
                    });
                  }
                });
                _self.carIndex = 0;
                // _self.subtotalAmount();
                _self.loading = false;
              } else {
                // 充值卡权益
                let item = balanceCardItem;
                item.discount = data.discount;
                item.cardName = item.card_info;
                item.membercard_id = item.id;
                item.cardDetailsId = data.card_details_id;
                let newObj = data;
                // item.id = item.card_id;
                newObj["specifications_id"] = 0;
                newObj["manualDiscount"] = data.equityTypes;
                newObj["manualDiscountCard"] = item;
                newObj["zhonglei"] = 3;
                newObj["discount"] = data.discount;
                newObj["choosemembercardId"] = data.card_details_id;
                newObj["chooseMemberCardCount"] = 0;
                newObj["isActiveCostCard"] = 1;
                newObj.judgeCard = 1;
                newObj.costCard = {};
                newObj.costCard.card_info = item.card_info;
                newObj["subtotal"] = newObj.buyNum * newObj.price;
                newObj["_animationId"] = Date.now() + Math.random();
                this.$set(data, "_justAdded", true);
                this.timerManager.createTimer(
                  "justAdded_" + data.id,
                  () => {
                    this.$set(data, "_justAdded", false);
                  },
                  600
                );
                _self.C_open_order_specifications_save.unshift(
                  this.deepCopy(newObj)
                );
                // 滚动到顶端
                this.$nextTick(() => {
                  if (this.$refs.rightScrollContainer) {
                    this.$refs.rightScrollContainer.scrollTo({
                      top: 0,
                      behavior: "smooth",
                    });
                  }
                });
                //设置carIndex
                _self.carIndex = 0;
                // _self.subtotalAmount();
                _self.loading = false;
              }
            } else if (data.equityType == 3) {
              // [INFO]
              let item = {
                cardName: cardInfo["card_name"],
                cardSource: cardInfo["cardSource"],
                card_type: cardInfo["cardtype"],
                discount: data["discount"] ? data["discount"] : "1",
                id: data["card_details_id"],
                indate: cardInfo["indateName"],
                isgive: data["givenum"] > 0 ? 1 : 2,
                maxnum: data["num"],
                membercard_id: cardInfo["card_id"],
                once_cardtype: cardInfo["once_cardtype"],
              };
              let newObj = data;
              newObj["specifications_id"] = 0;
              newObj["manualDiscount"] = 3;
              newObj["manualDiscountCard"] = item;
              if (data.goods_type == 2) {
                // zhonglei 1 服务 2 产品 3 服务
                newObj["zhonglei"] = 2;
                newObj["is_open_product_jishi"] = true;
              } else {
                newObj["zhonglei"] = 3;
              }
              newObj["discount"] = item.discount;
              newObj["choosemembercardId"] = data.card_details_id;
              newObj["chooseMemberCardCount"] = 0;
              newObj["isActiveCostCard"] = 1;
              newObj.judgeCard = 1;
              newObj.costCard = {};
              newObj.costCard.card_info = "不使用耗卡";
              // console.log(newObj);
              newObj["num"] = 1;
              newObj["buyNum"] = 1;
              newObj["unitPrice"] = newObj.price;
              newObj["originPrice"] = newObj.price;
              newObj["average_price"] = newObj.price;
              newObj["subtotal"] = 0;
              newObj["_animationId"] = Date.now() + Math.random();
              this.$set(data, "_justAdded", true);
              this.timerManager.createTimer(
                "justAdded_" + data.id,
                () => {
                  this.$set(data, "_justAdded", false);
                },
                600
              );
              _self.C_open_order_specifications_save.unshift(
                this.deepCopy(newObj)
              );
              // 滚动到顶端
              this.$nextTick(() => {
                if (_self.$refs.rightScrollContainer) {
                  _self.$refs.rightScrollContainer.scrollTo({
                    top: 0,
                    behavior: "smooth",
                  });
                }
              });
              //设置carIndex
              _self.carIndex = 0;
              // _self.subtotalAmount();
              _self.loading = false;
            }
          }
        },
        // 扣卡减按钮
        handleNumInputMinus: function (index) {
          // 立即触发对应searchCardBox项的高亮和滚动
          this.highlightAndScrollSearchCard(index);

          const result = this.C_open_order_specifications_save[index].num - 1;
          if (result <= 0) {
            this.open_details_price_del(index);
          } else {
            this.C_open_order_specifications_save[index].num = result;
          }
        },
        // 扣卡加按钮
        handleNumInputPlus: function (index) {
          // 立即触发对应searchCardBox项的高亮和滚动
          this.highlightAndScrollSearchCard(index);

          // 根据card_id及服务id匹配哪个服务
          const currentItem = this.sampleMemberCardInfo.filter((item) => {
            if (
              item.cardInfo?.card_id ==
                this.C_open_order_specifications_save[index]?.cardInfo
                  .card_id &&
              item.card_details_id ==
                this.C_open_order_specifications_save[index].card_details_id
            ) {
              return true;
            }
          })[0];
          // 当前服务有多少个
          let currentUseNum = 0;
          this.timerCardUse.forEach((item) => {
            if (
              currentItem.cardInfo.card_id == item["consumeCardId"] &&
              currentItem.card_details_id == item["cardDetailsId"]
            ) {
              currentUseNum = currentUseNum + item.num;
            }
          });

          if (currentItem.num == -1 || currentItem.num - currentUseNum > 0) {
            this.C_open_order_specifications_save[index].num =
              this.C_open_order_specifications_save[index].num + 1;
          } else {
            return this.$message({
              type: "error",
              message: "可用次数不足",
              center: true,
              duration: 1500,
            });
          }
        },
        // 高亮并滚动到对应的searchCardBox项
        highlightAndScrollSearchCard: function (selectIndex) {
          try {
            const selectedItem =
              this.C_open_order_specifications_save[selectIndex];
            if (!selectedItem) return;

            // 在sampleMemberCardInfo中找到对应的项
            const searchCardIndex = this.sampleMemberCardInfo.findIndex(
              (item) => {
                return (
                  item.cardInfo?.card_id == selectedItem?.cardInfo?.card_id &&
                  item.card_details_id == selectedItem.card_details_id
                );
              }
            );

            if (searchCardIndex !== -1) {
              this.$nextTick(() => {
                try {
                  // 找到对应的searchCardBox元素并添加高亮效果
                  const searchCardBoxes = this.getSearchCardBoxes();
                  if (searchCardBoxes && searchCardBoxes[searchCardIndex]) {
                    const targetCard = searchCardBoxes[searchCardIndex];

                    // 滚动到对应的项目
                    if (targetCard.scrollIntoView) {
                      targetCard.scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                      });
                    }

                    // 清除之前的定时器（如果存在）
                    const timerKey = "searchCardHighlight_" + searchCardIndex;
                    this.timerManager.clearTimer(timerKey);

                    // 先移除动画类，确保能重新触发动画
                    targetCard.classList.remove("o-service-card-pulse");

                    // 强制重绘，确保类被移除
                    targetCard.offsetHeight;

                    // 重新添加高亮类
                    targetCard.classList.add("o-service-card-pulse");

                    // 延迟移除高亮类
                    this.timerManager.createTimer(
                      timerKey,
                      () => {
                        if (targetCard && targetCard.classList) {
                          targetCard.classList.remove("o-service-card-pulse");
                        }
                      },
                      1000
                    );
                  }
                } catch (nextTickError) {
                  console.error(
                    "highlightAndScrollSearchCard nextTick 出错:",
                    nextTickError
                  );
                }
              });
            }
          } catch (error) {
            console.error("highlightAndScrollSearchCard 方法出错:", error);
          }
        },
        // 开始连续添加
        /* startContinuousAdd: function () {
          if (!this.isLongPressing || !this.isLeftPressed) return;

          // 第一次+10在200ms后执行
          this.longPressFirstTimer = this.timerManager.createTimer(
            "longPressFirst",
            () => {
              if (!this.isLongPressing || !this.isLeftPressed) return;

              // 第一次添加10个数量
              this.addQuantity(this.leftPressData, this.leftPressEvent, 10);

              // 后续每1000ms执行一次
              this.longPressInterval = this.timerManager.createTimer(
                "longPressInterval",
                () => {
                  if (!this.isLongPressing || !this.isLeftPressed) {
                    this.timerManager.clearTimer("longPressInterval");
                    return;
                  }

                  // 添加10个数量
                  this.addQuantity(this.leftPressData, this.leftPressEvent, 10);
                },
                1000
              );
            },
            200
          );
        }, */
        // 添加服务数量（支持指定数量）
        /*  addQuantity: function (data, event, quantity = 1) {
          try {
            // 参数验证
            if (!data || typeof data !== "object" || !data.id) {
              console.error("addQuantity: 无效的数据对象");
              return;
            }

            // 数量验证
            const safeQuantity = parseInt(quantity) || 1;
            if (safeQuantity <= 0 || safeQuantity > 9999) {
              console.error("addQuantity: 无效的数量");
              return;
            }

            // 安全的深拷贝
            this.data_server_product = this.safeExecute(() => {
              return this.deepCopy(data);
            }, "addQuantity 深拷贝");

            if (data.status !== 1) {
              this.$message({
                message: "该商品已经下架",
                center: true,
                duration: 2600,
              });
              return;
            }

            // 确保数组存在
            if (!Array.isArray(this.C_open_order_specifications_save)) {
              this.C_open_order_specifications_save = [];
            }

            // 安全查找是否已存在该服务
            let duplicateIndex = -1;
            for (
              let i = 0;
              i < this.C_open_order_specifications_save.length;
              i++
            ) {
              const item = this.C_open_order_specifications_save[i];
              if (
                item &&
                typeof item === "object" &&
                data["id"] == item["id"]
              ) {
                duplicateIndex = i;
                break;
              }
            }

            if (duplicateIndex !== -1) {
              // 如果服务已存在，增加指定数量
              const item =
                this.C_open_order_specifications_save[duplicateIndex];
              const newQuantity = Math.min((item.num || 0) + quantity, 9999);
              const actualAdded = newQuantity - (item.num || 0);

              if (actualAdded > 0) {
                item.num = newQuantity;

                // 重新计算价格
                const unitPrice = parseFloat(item.unitPrice) || 0;
                const subtotal = (unitPrice * item.num).toFixed(2);
                item.subtotal = subtotal;
                item.totalAmount = Math.round(unitPrice * item.num * 100);

                // 显示弹出数字效果
                if (event && event.clientX && event.clientY) {
                  this.createPopupNumber(
                    event.clientX,
                    event.clientY,
                    newQuantity,
                    quantity >= 10,
                    false
                  );
                }

                // 滚动到对应项并高亮
                this.$nextTick(() => {
                  const serviceCards = this.getServiceCards();
                  if (serviceCards && serviceCards[duplicateIndex]) {
                    serviceCards[duplicateIndex].scrollIntoView({
                      behavior: "smooth",
                      block: "center",
                    });

                    serviceCards[duplicateIndex].classList.add(
                      "o-service-card-highlight"
                    );
                    this.timerManager.createTimer(
                      "highlight_" + duplicateIndex,
                      () => {
                        if (serviceCards[duplicateIndex]) {
                          serviceCards[duplicateIndex].classList.remove(
                            "o-service-card-highlight"
                          );
                        }
                      },
                      1000
                    );
                  }
                });

                this.calculatePrice();
              }
            } else {
              // 如果服务不存在，添加新服务
              const newData = this.deepCopy(data);
              newData["num"] = quantity;
              newData["price"] = data.price || 0;
              newData["unitPrice"] = data.price || 0;
              newData["totalAmount"] = (data.price || 0) * quantity * 100;
              newData["subtotal"] = ((data.price || 0) * quantity).toFixed(2);
              newData["_animationId"] = Date.now() + Math.random();
              newData["technician_id"] = [];
              newData["salesmen"] = [];
              newData["manualDiscount"] = 1;
              newData["manualDiscountCard"] = {};
              newData["zhonglei"] = 1;

              this.$set(data, "_justAdded", true);
              this.timerManager.createTimer(
                "justAdded_" + data.id,
                () => {
                  this.$set(data, "_justAdded", false);
                },
                600
              );

              this.C_open_order_specifications_save.unshift(newData);

              // 显示弹出数字效果
              if (event && event.clientX && event.clientY) {
                this.createPopupNumber(
                  event.clientX,
                  event.clientY,
                  quantity,
                  quantity >= 10,
                  false
                );
              }

              // 滚动到顶端
              this.$nextTick(() => {
                if (this.$refs.rightScrollContainer) {
                  this.$refs.rightScrollContainer.scrollTo({
                    top: 0,
                    behavior: "smooth",
                  });
                }
              });

              this.calculatePrice();
            }
          } catch (error) {
            console.error("addQuantity 方法出错:", error);
            this.$message({
              type: "error",
              message: "添加服务时发生错误",
              duration: 2000,
            });
          }
        }, */
        // 统一的鼠标按下处理
        /* handleMouseDown: function (value, event) {
          event.preventDefault();
          event.stopPropagation();

          if (event.button === 0) {
            // 左键按下
            this.handleLeftMouseDown(value, event);
          } else if (event.button === 2) {
            // 右键按下
            this.handleRightMouseDown(value, event);
          }
        }, */

        // 左键按下处理
        /* handleLeftMouseDown: function (value, event) {
          this.leftPressStartTime = performance.now();
          this.leftPressData = value;
          this.leftPressEvent = event;
          this.isLeftPressed = true;
          this.isLongPressing = false;
          this.wasLongPressed = false;

          // 400ms后开始长按模式
          this.longPressTimer = this.timerManager.createTimer(
            "longPressStart",
            () => {
              if (this.isLeftPressed) {
                this.isLongPressing = true;
                this.startContinuousAdd();
              }
            },
            400
          );
        }, */

        // 右键按下处理
        /* handleRightMouseDown: function (value, event) {
          this.rightPressStartTime = performance.now();
          this.rightLongPressData = value;
          this.rightLongPressEvent = event;
          this.isRightPressed = true;
          this.isRightLongPressing = false;
          this.wasRightLongPressed = false;

          // 400ms后开始右键长按模式
          this.rightLongPressTimer = this.timerManager.createTimer(
            "rightLongPressStart",
            () => {
              if (this.isRightPressed) {
                this.isRightLongPressing = true;
                this.startRightContinuousRemove();
              }
            },
            400
          );
        }, */

        // 统一的鼠标松开处理
        /* handleMouseUp: function (event) {
          if (event.button === 0) {
            this.handleLeftMouseUp(event);
          } else if (event.button === 2) {
            this.handleRightMouseUp(event);
          }
        }, */

        // 左键松开处理
        /* handleLeftMouseUp: function (event) {
          if (!this.isLeftPressed) return;

          const endTime = performance.now();
          const pressDuration = endTime - (this.leftPressStartTime || 0);
          const wasInLongPressMode = this.isLongPressing;

          // 清理状态和定时器
          this.isLeftPressed = false;
          this.clearLeftPressTimers();

          // 如果是短按且没有进入长按模式，执行单击逻辑
          if (pressDuration < 400 && !wasInLongPressMode) {
            this.addQuantity(this.leftPressData, this.leftPressEvent, 1);
          } else if (wasInLongPressMode) {
            // 设置长按结束标记
            this.wasLongPressed = true;
            this.timerManager.createTimer(
              "resetLongPressed",
              () => {
                this.wasLongPressed = false;
              },
              100
            );
          }

          this.isLongPressing = false;
        }, */

        // 右键松开处理
        /* handleRightMouseUp: function (event) {
          if (!this.isRightPressed) return;

          const endTime = performance.now();
          const pressDuration = endTime - (this.rightPressStartTime || 0);
          const wasInLongPressMode = this.isRightLongPressing;

          // 清理状态和定时器
          this.isRightPressed = false;
          this.clearRightLongPressTimers();

          // 如果是短按且没有进入长按模式，执行单击逻辑
          if (pressDuration < 400 && !wasInLongPressMode) {
            this.removeServiceQuantity(
              this.rightLongPressData,
              this.rightLongPressEvent,
              1
            );
          } else if (wasInLongPressMode) {
            // 设置长按结束标记
            this.wasRightLongPressed = true;
            this.timerManager.createTimer(
              "resetRightLongPressed",
              () => {
                this.wasRightLongPressed = false;
              },
              100
            );
          }

          this.isRightLongPressing = false;
        }, */

        // 全局左键松开处理
        /* handleGlobalLeftMouseUp: function (event) {
          if (this.isLeftPressed || this.isLongPressing) {
            this.handleLeftMouseUp(event);
          }
        }, */

        // 全局右键松开处理
        /* handleGlobalRightMouseUp: function (event) {
          if (this.isRightPressed || this.isRightLongPressing) {
            this.handleRightMouseUp(event);
          }
        }, */

        // 清理左键相关定时器
        /* clearLeftPressTimers: function () {
          this.timerManager.clearTimer("longPressStart");
          this.timerManager.clearTimer("longPressFirst");
          this.timerManager.clearTimer("longPressInterval");
          this.longPressTimer = null;
          this.longPressFirstTimer = null;
          this.longPressInterval = null;
        }, */

        // 清除右键长按定时器
        /* clearRightLongPressTimers: function () {
          this.timerManager.clearTimer("rightLongPressStart");
          this.timerManager.clearTimer("rightLongPressFirst");
          this.timerManager.clearTimer("rightLongPressInterval");
          this.rightLongPressTimer = null;
          this.rightLongPressFirstTimer = null;
          this.rightLongPressInterval = null;
        }, */

        // 处理右键点击
        /* handleRightClick: function (value, event) {
          // 阻止右键菜单，实际逻辑在mousedown/mouseup中处理
          event.preventDefault();
          event.stopPropagation();
          return false;
        }, */
        // 开始右键连续减少
        /* startRightContinuousRemove: function () {
          if (!this.isRightLongPressing || !this.isRightPressed) return;

          // 第一次-10在200ms后执行
          this.rightLongPressFirstTimer = this.timerManager.createTimer(
            "rightLongPressFirst",
            () => {
              if (!this.isRightLongPressing || !this.isRightPressed) return;

              // 第一次减少10个数量
              this.removeServiceQuantity(
                this.rightLongPressData,
                this.rightLongPressEvent,
                10
              );

              // 后续每1000ms执行一次
              this.rightLongPressInterval = this.timerManager.createTimer(
                "rightLongPressInterval",
                () => {
                  if (!this.isRightLongPressing || !this.isRightPressed) {
                    this.timerManager.clearTimer("rightLongPressInterval");
                    return;
                  }

                  // 减少10个数量
                  this.removeServiceQuantity(
                    this.rightLongPressData,
                    this.rightLongPressEvent,
                    10
                  );
                },
                1000
              );
            },
            200
          );
        }, */
        // 减少服务数量（支持指定数量）
        /*  removeServiceQuantity: function (value, event, quantity = 1) {
          try {
            if (!value || !value.id) {
              console.error("removeServiceQuantity: 无效的数据对象");
              return;
            }

            const data = value;

            // 查找是否存在该服务
            let existingIndex = -1;
            for (
              let i = 0;
              i < this.C_open_order_specifications_save.length;
              i++
            ) {
              if (
                data["id"] == this.C_open_order_specifications_save[i]["id"]
              ) {
                existingIndex = i;
                break;
              }
            }

            if (existingIndex !== -1) {
              const item = this.C_open_order_specifications_save[existingIndex];
              const currentNum = item.num || 0;
              const newQuantity = Math.max(currentNum - quantity, 0);
              const actualRemoved = currentNum - newQuantity;

              if (actualRemoved > 0) {
                if (newQuantity === 0) {
                  // 如果数量变为0，删除该服务项
                  this.$set(item, "_removing", true);
                  this.timerManager.createTimer(
                    "removeService_" + existingIndex,
                    () => {
                      if (
                        existingIndex <
                        this.C_open_order_specifications_save.length
                      ) {
                        this.C_open_order_specifications_save.splice(
                          existingIndex,
                          1
                        );
                        this.calculatePrice();
                      }
                    },
                    100
                  );
                } else {
                  // 更新数量
                  item.num = newQuantity;

                  // 重新计算价格
                  const unitPrice = parseFloat(item.unitPrice) || 0;
                  const subtotal = (unitPrice * item.num).toFixed(2);
                  item.subtotal = subtotal;
                  item.totalAmount = Math.round(unitPrice * item.num * 100);

                  this.calculatePrice();
                }

                // 显示弹出数字效果（负数）
                if (event && event.clientX && event.clientY) {
                  this.createPopupNumber(
                    event.clientX,
                    event.clientY,
                    newQuantity,
                    quantity >= 10,
                    true
                  );
                }

                // 滚动到对应项并高亮
                if (newQuantity > 0) {
                  this.$nextTick(() => {
                    const serviceCards = this.getServiceCards();
                    if (serviceCards && serviceCards[existingIndex]) {
                      serviceCards[existingIndex].scrollIntoView({
                        behavior: "smooth",
                        block: "center",
                      });

                      serviceCards[existingIndex].classList.add(
                        "o-service-card-highlight"
                      );
                      this.timerManager.createTimer(
                        "rightHighlight_" + existingIndex,
                        () => {
                          if (serviceCards[existingIndex]) {
                            serviceCards[existingIndex].classList.remove(
                              "o-service-card-highlight"
                            );
                          }
                        },
                        1000
                      );
                    }
                  });
                }
              }
            }
          } catch (error) {
            console.error("removeServiceQuantity 方法出错:", error);
            this.$message({
              type: "error",
              message: "减少服务数量时发生错误",
              duration: 2000,
            });
          }
        }, */
        //
        /**
         * 创建弹出数字效果
         * @param {*} x 鼠标位置
         * @param {*} y 鼠标位置
         * @param {*} number 显示的内容
         * @param {*} isLongPress 是否长按
         * @param {*} isDecrease 是否减少
         */
        /*  createPopupNumber: function (
          x,
          y,
          number,
          isLongPress = false,
          isDecrease = false
        ) {
          try {
            // 验证输入参数
            if (
              typeof x !== "number" ||
              typeof y !== "number" ||
              isNaN(x) ||
              isNaN(y)
            ) {
              console.warn("createPopupNumber: 无效的坐标参数");
              return;
            }

            const popup = document.createElement("div");
            const popupAdd = document.createElement("div");

            // 根据操作类型和模式设置样式类
            let className = "o-number-popup ";
            let classNameAdd = "o-number-popup-add ";
            // 单击用蓝色，长按用紫色
            className += isLongPress
              ? "o-number-popup-purple"
              : "o-number-popup-blue";

            if (isDecrease) {
              // 减少操作：单击用蓝色，长按用红色
              classNameAdd += "o-number-popup-red";
              popupAdd.textContent = isLongPress ? "-10" : "-1";
            } else {
              // 增加操作：单击用蓝色，长按用红色
              classNameAdd += isLongPress
                ? "o-number-popup-purple"
                : "o-number-popup-blue";
              popupAdd.textContent = isLongPress ? "+10" : "+1";
            }

            popup.className = className;
            popupAdd.className = classNameAdd;
            popup.textContent = (number || 0).toString();
            popup.style.left = Math.max(0, x) + "px";
            popupAdd.style.left = Math.max(0, x) + "px";
            popup.style.top = Math.max(0, y) + "px";
            popupAdd.style.top = Math.max(0, y) + "px";
            popup.style.position = "fixed";
            popupAdd.style.position = "fixed";
            popup.style.zIndex = "9999";
            popupAdd.style.zIndex = "9999";

            if (document.body) {
              document.body.appendChild(popup);
              document.body.appendChild(popupAdd);

              // 动画结束后移除元素
              this.timerManager.createTimer(
                "popupCleanup_" + Date.now() + "_" + Math.random(),
                () => {
                  try {
                    if (popup && popup.parentNode) {
                      popup.parentNode.removeChild(popup);
                    }
                    if (popupAdd && popupAdd.parentNode) {
                      popupAdd.parentNode.removeChild(popupAdd);
                    }
                  } catch (cleanupError) {
                    console.warn("清理弹出数字元素时出错:", cleanupError);
                  }
                },
                1000
              );
            }
          } catch (error) {
            console.error("createPopupNumber 方法出错:", error);
          }
        }, */

        //每次添加服务和删除服务都会触发价格的事件
        calculatePrice: function () {
          try {
            if (this.billingType == "2") return;

            this.pay_all = 0;

            // 增加更严格的数据验证
            if (
              !this.C_open_order_specifications_save ||
              !Array.isArray(this.C_open_order_specifications_save) ||
              this.C_open_order_specifications_save.length == 0
            ) {
              this.pay_all_show = "0.00";
              return;
            }

            // 使用更安全的循环方式
            for (
              let i = 0;
              i < this.C_open_order_specifications_save.length;
              i++
            ) {
              const item = this.C_open_order_specifications_save[i];
              if (
                item &&
                typeof item === "object" &&
                typeof item.totalAmount === "number"
              ) {
                // 验证数值有效性
                if (!isNaN(item.totalAmount) && isFinite(item.totalAmount)) {
                  this.pay_all += item.totalAmount;
                }
              }
            }

            // 确保结果是有效数字
            if (isNaN(this.pay_all) || !isFinite(this.pay_all)) {
              this.pay_all = 0;
            }

            this.pay_all_show = (this.pay_all / 100).toFixed(2);
          } catch (error) {
            console.error("calculatePrice 方法出错:", error);
            this.pay_all = 0;
            this.pay_all_show = "0.00";
          }
        },
        //减少充次卡的次数
        jianshao: function (index) {
          const item = this.C_open_order_specifications_save[index];

          if (item.num > 1) {
            item.num--;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.num).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.num * 100);

          this.calculatePrice();
        },
        //增加充次卡的次数
        zengjia: function (index) {
          const item = this.C_open_order_specifications_save[index];

          if (item.num < 9999) {
            item.num++;
          }

          // 根据单价和数量计算小计
          const unitPrice = parseFloat(item.unitPrice) || 0;
          const subtotal = (unitPrice * item.num).toFixed(2);
          item.subtotal = subtotal;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(unitPrice * item.num * 100);

          this.calculatePrice();
        },
        handleNumInputChange: function (e, index) {
          try {
            if (
              !e ||
              !e.target ||
              index < 0 ||
              index >= this.C_open_order_specifications_save.length
            ) {
              console.error("handleNumInputChange: 无效的参数");
              return;
            }

            const item = this.C_open_order_specifications_save[index];
            if (!item) {
              console.error("handleNumInputChange: 找不到对应的项目");
              return;
            }

            const numValue = parseInt(e.target.value);

            // 验证输入值是否为有效数字
            if (isNaN(numValue)) {
              // 如果输入无效，重置为1
              item.num = 1;
            } else {
              // 限制数量范围在1-9999之间
              if (numValue < 1) {
                item.num = 1;
              } else if (numValue > 9999) {
                item.num = 9999;
              } else {
                item.num = numValue;
              }
            }

            // 根据单价和数量计算小计
            const unitPrice = parseFloat(item.unitPrice) || 0;
            const subtotal = (unitPrice * item.num).toFixed(2);
            item.subtotal = subtotal;

            // 更新totalAmount (以分为单位)
            item.totalAmount = Math.round(unitPrice * item.num * 100);

            // 重新计算总价
            this.calculatePrice();
          } catch (error) {
            console.error("handleNumInputChange 方法出错:", error);
          }
        },

        //调整小计
        handleSubtotalChange: function (index) {
          const item = this.C_open_order_specifications_save[index];
          const subtotal = parseFloat(item.subtotal) || 0;
          const quantity = parseInt(item.num) || 1;

          // 根据小计和数量计算单价 = 小计 / 数量
          const unitPrice =
            quantity > 0 ? (subtotal / quantity).toFixed(2) : "0.00";
          item.unitPrice = unitPrice;

          // 更新totalAmount (以分为单位)
          item.totalAmount = Math.round(subtotal * 100);

          // 重新计算总价
          this.calculatePrice();
        },
      },
      filters: {
        ellipsis(value) {
          if (!value) return "";
          if (value.length > 12) {
            return value.slice(0, 12) + "...";
          }
          return value;
        },

        // 格式化充值金额/100
        filterMoney: function (money) {
          money = money ? money : 0;
          return (money / 100).toFixed(2);
        },
      },
      watch: {
        login: {
          handler(n) {
            this.loginInfo = n;
          },
          deep: true,
          immediate: true,
        },
        isShowLoseEfficacyCard: {
          handler(n) {
            this.getMemberStampCardInfo();
          },
        },
        C_open_order_specifications_save: {
          immediate: true,
          deep: true,
          handler(save_orderData, o) {
            try {
              console.log(save_orderData, "C_open_order_specifications_save");

              if (!Array.isArray(save_orderData)) {
                console.warn("save_orderData 不是数组");
                this.timerCardUse = [];
                return;
              }

              let orderItems = [];
              let timerCardUse = [];

              save_orderData.forEach((item, i) => {
                try {
                  if (!item || typeof item !== "object") {
                    console.warn(`第 ${i} 项数据无效`);
                    return;
                  }

                  const manualDiscountCard = item.manualDiscountCard || {};
                  let cardDetailsId = 0;

                  if (manualDiscountCard.cardSource != -1) {
                    cardDetailsId =
                      manualDiscountCard["cardDetailsId"] ||
                      manualDiscountCard["id"] ||
                      0;
                  } else if (item.manualDiscount == 3) {
                    cardDetailsId =
                      manualDiscountCard["cardDetailsId"] ||
                      manualDiscountCard["id"] ||
                      0;
                  }

                  let cardId = 0;
                  if (manualDiscountCard.cardSource != -1) {
                    cardId = manualDiscountCard["membercard_id"] || 0;
                  } else if (item.manualDiscount == 3) {
                    cardId = manualDiscountCard["membercard_id"] || 0;
                  }

                  let consumeCardId = cardId;
                  if (item.costCard && item.costCard["membercard_id"]) {
                    consumeCardId = item.costCard["membercard_id"];
                  }

                  if (item.manualDiscount == 3) {
                    // 次卡类型
                    let onceCardtype = manualDiscountCard.once_cardtype
                      ? manualDiscountCard.once_cardtype
                      : 1;
                    timerCardUse.push({
                      carIndex: i,
                      cardDetailsId: cardDetailsId,
                      cardId: cardId,
                      consumeCardId: consumeCardId,
                      cardName: manualDiscountCard["cardName"] || "",
                      once_cardtype: onceCardtype,
                      num: item.num || 1,
                      money: 0,
                      goodsId: item.id || 0, // 商品id 服务，产品卡项都填写id
                      itemId: item.itemId || 0, //取单时候传的itemId
                      itemImgId: item.itemImgId || "0", // 预览图id
                      itemName: item.service_name || item.product_name || "", // 商品名称
                      itemType: item.zhonglei == 2 ? 2 : 1, // 1 服务 2产品 3卡项 4充值
                      isgive: manualDiscountCard.isgive || 0,
                    });
                  }
                } catch (itemError) {
                  console.error(`处理第 ${i} 项数据时出错:`, itemError);
                }
              });

              this.timerCardUse = timerCardUse;
            } catch (error) {
              console.error(
                "C_open_order_specifications_save watcher 出错:",
                error
              );
              this.timerCardUse = [];
            }
          },
        },
      },
      beforeDestroy: function () {
        try {
          console.log("billPage 组件开始销毁，清理资源...");

          // 清理长按定时器
          // this.clearLeftPressTimers();
          // this.clearRightLongPressTimers();

          // 清理所有定时器
          if (
            this.timerManager &&
            typeof this.timerManager.clearAllTimers === "function"
          ) {
            this.timerManager.clearAllTimers();
          }

          // 清理全局事件监听器
          /* if (
            this.globalMouseUpHandler &&
            document &&
            typeof document.removeEventListener === "function"
          ) {
            document.removeEventListener("mouseup", this.globalMouseUpHandler);
            this.globalMouseUpHandler = null;
          } */

          // 清理数组引用，防止内存泄漏
          if (Array.isArray(this.C_open_order_specifications_save)) {
            this.C_open_order_specifications_save.length = 0;
          }
          if (Array.isArray(this.cashier_open_order_service_name)) {
            this.cashier_open_order_service_name.length = 0;
          }
          if (Array.isArray(this.cashier_open_order_product_name)) {
            this.cashier_open_order_product_name.length = 0;
          }
          if (Array.isArray(this.timerCardUse)) {
            this.timerCardUse.length = 0;
          }
          if (Array.isArray(this.sampleMemberCardInfo)) {
            this.sampleMemberCardInfo.length = 0;
          }
          if (Array.isArray(this.sampleMemberCardInfo_runOut)) {
            this.sampleMemberCardInfo_runOut.length = 0;
          }

          // 重置状态
          this.isLeftPressed = false;
          this.isRightPressed = false;
          this.isLongPressing = false;
          this.isRightLongPressing = false;
          this.wasLongPressed = false;
          this.wasRightLongPressed = false;

          // 清理对象引用
          this.leftPressData = null;
          this.leftPressEvent = null;
          this.rightLongPressData = null;
          this.rightLongPressEvent = null;
          this.data_server_product = null;

          console.log("billPage 组件资源清理完成");
        } catch (error) {
          console.error("beforeDestroy 清理资源时出错:", error);
        }
      },
    });
  });
});
