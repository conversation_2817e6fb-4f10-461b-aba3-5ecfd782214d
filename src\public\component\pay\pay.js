Vue.component("f-pay-btn", function (resolve, reject) {
  $.get("component/pay/payBtn.html").then(function (res) {
    resolve({
      template: res,
      props: {
        title: {
          type: String,
        },
        "is-laka-selected": {
          type: <PERSON><PERSON>an,
          value: false,
        },
        "is-laka": {
          type: <PERSON><PERSON>an,
          value: false,
        },
        "insufficient-balance": {
          type: Boolean,
          value: false,
        },
      },
      computed: {
        isShowLaKalaSelectBtn: function () {
          return this.isLakaSelected;
        },
        btnBoxClassName: function () {
          // 如果余额不足，直接返回禁用状态
          if (this.insufficientBalance) {
            return "is-disable";
          }
          // 只有在需要显示拉卡拉选择按钮时才进行判断
          if (this.isShowLaKalaSelectBtn) {
            return this.isLaka ? "is-active" : "is-disable";
          }
          // 默认返回空类名
          return "";
        },
      },
    });
  });
});

Vue.component("app-pay", function (resolve, reject) {
  $.get("component/pay/pay.html").then(function (res) {
    resolve({
      template: res,
      props: {
        billToPay: {
          type: Number,
        },
        loginInfo: {
          type: Object,
        },
        "buy-receipt": {
          type: Boolean,
        },
        "order-no": {
          type: String,
        },
        "use-card": {
          type: Boolean,
        },
        isPayStatus: {
          type: null,
        },
        isDebtFlag: {
          type: Boolean,
        },
        // 是否扣卡
        isConsumeCards: {
          type: Boolean,
        },
      },

      errorCaptured: function (err, instance, info) {
        console.error("Pay component error captured:", err, info);
        return false; // 阻止错误继续传播
      },

      data: function () {
        return {
          timerManager: ff_util.timerManager,
          isRecharge: false,
          printing: false,
          payWrapInnerLeft: 160,
          isLakalaSelected: false,
          isShowLakalaDialog: false,
          isLimitPrice: true,
          url: baseUrl,
          userInfo: {},
          //充次卡金额
          composeMoney: "", ////现金那一栏输入框中的值
          cash: 0, //现金那一栏输入框中的值
          discountComposeMoney: 0, //充次卡页面跳转到收银台的左下角折扣金额
          cz_qudan: true, //收款弹窗
          kd_kaidanxinxi: {}, // 收款开单信息
          kd_xinxi_list: {}, // 开单信息列表
          kd_xinxi_list_buyer: {},
          kd_xinxi_list_cashierInfo: {}, //
          orderListLoading: true,
          getOrderDetailsLoading: true,
          orderDetails: {},
          payCardInfo: [],
          requisiteCard: [],
          paymentOffer: [],
          checkedOffer: [],
          cz_shou_qian: "", //现金那一栏输入框中的值
          paymentCode: "", //微信支付宝付款码
          isPay: 4,
          cz_chongzhika: [],
          scorllData: {
            height_1: "100px",
            height_2: "0px",
          },
          paymentBtn: {
            width: "250px",
            height: "50px",
            bottom: "36px",
            right: "70px",
            text: "收款",
            vertical: false,
          },
          resizeHandler: null, // 窗口大小变化监听器
          zeroMwitch: false, //抹零开关

          vipPass: "", //会员密码
          returnType: 3, // 1，多余定金转入会员默认账户，2，退回现金，3，多余定金原路退回
          orderNumber: "", // 订单号
          isCard: false,
          // 键盘
          value1: "1",
          value2: "2",
          value3: "3",
          zj_price_menu: [
            {
              key1: "7",
              key2: "8",
              key3: "9",
            },
            {
              key1: "4",
              key2: "5",
              key3: "6",
            },
            {
              key1: "1",
              key2: "2",
              key3: "3",
            },
            {
              key1: "00",
              key2: "0",
              key3: "•",
            },
          ],
          zj_all_num: [
            {
              key: "01",
              value: "7",
            },
            {
              key: "02",
              value: "8",
            },
            {
              key: "03",
              value: "9",
            },
            {
              key: "11",
              value: "4",
            },
            {
              key: "12",
              value: "5",
            },
            {
              key: "13",
              value: "6",
            },
            {
              key: "21",
              value: "1",
            },
            {
              key: "22",
              value: "2",
            },
            {
              key: "23",
              value: "3",
            },
            {
              key: "31",
              value: "00",
            },
            {
              key: "32",
              value: "0",
            },
            {
              key: "33",
              value: ".",
            },
          ],
          selectId: "", //选中id
          // residuebalance: 0,
          remaining: 0, // 待支付

          paperwidth: 0, //打印纸宽度
          printSet: [], // 设置打印
          endPay: 0, //是否支付完成
          showMoney: 0,
          showMoneys: 0, //展示在待支付的money
          selectedCard: [], //选中的卡数组
          SelectPreferenceSequence: -1,
          cz_shou_qians: 0.0,
          orderId: 0,
          collectionTime: "",
          //修改员工业绩
          isModifyPerformance: false,
          isModify: true,
          zuhekaPerformance: 1, //所有业绩提成信息
          totalPerformance: {},
          performanceList: [], //业绩提成信息列表
          salesmenList: [], //销售业绩列表
          techniciansList: [], //服务人员业绩列表
          isSales: false, //选择销售弹框
          isCrafts: false, //选择服务人员弹框
          isHandelIndex: 0, //每条服务在列表中的下标
          AllSales: [], //所有销售和服务人员
          AllCrafts: [], //所有服务人员
          deductType: [
            {
              name: "比例提成",
              id: 1,
            },
            {
              name: "固定提成",
              id: 2,
            },
          ], //提成方式下拉框
          isDeductType: "", //选择的提成方式
          salesChecked: [], //选中的销售存储
          craftsChecked: [], //存储选中的服务人员
          allDelect: [],
          saveModifyArr: [], //存储修改的数据
          addArr: [], //存储增添的数据
          delArr: [], //存储删除的数据
          modifyEmployeeStatus: undefined, //判断是否显示员工业绩按钮
          loginModify: undefined,
          isReturnZero: false, //抹零
          formerMoney: 0, //抹零前的金额
          formrCash: 0, //抹零前的cash
          unChangedCash: 0, //不变的cash
          smallChangeMoney: 0, //抹零金额
          customizePayTypeList: [], //自定义记账方式数组
          custmizePayType: 0, //自定义记账payType

          //会员消费弹框
          vipComsumeConfirm: false,
          vipPassComfirm: false,
          vipPassCodeComfirm: false,
          vipPassword: "",
          vipPassCode: "",

          //收银台套餐卡，判断卡是不是次卡的字段
          sellCardType: 0, //判断卡是不是次卡的字段
          cardPayCardInfo: [], //套餐卡需要付款的卡的数组

          //欠款
          isDebt: false, //欠款弹框
          debtForm: {
            orderMoney: 0,
            payMoney: "",
            debtMoney: 0,
          },
          isDebtMoney: false,
          debtFlag: false, //去支付欠账标志
          // 手动开单时间
          manualOrderTime: "",
          isEditOrderTime: false,
          linkPaymentId: 0,
          custmizePaySearchForm: {
            amount: "",
            date: "",
          },
          isLoading: false,
          isPaying: false,
          paymentList: [],
          paymentLoading: false,
          limit: 20,
          currentPage: 1,
          allCount: 0,
          // 防重复调用标志
          _isClosing: false,
          pickerOptions: {
            shortcuts: [
              {
                text: "最近一周",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近一个月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                  picker.$emit("pick", [start, end]);
                },
              },
              {
                text: "最近三个月",
                onClick(picker) {
                  const end = new Date();
                  const start = new Date();
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                  picker.$emit("pick", [start, end]);
                },
              },
            ],
          },
        };
      },
      mounted: function () {
        try {
          const _self = this;
          _self.isLoading = true;

          // 延迟150ms加载，避免与按钮CSS动画冲突
          setTimeout(() => {
            try {
              // 安全地获取自定义支付类型
              _self.getCustomizePayType();

              // 延迟DOM操作，确保组件完全挂载
              _self.$nextTick(() => {
                try {
                  // 将组件移动到body下
                  _self.moveToBody();

                  // 获取订单详情
                  _self.fetchOrderDetail(() => {
                    try {
                      _self.bindPay(_self.isPayStatus);
                    } catch (error) {
                      console.error("bindPay error:", error);
                    }
                  });

                  // 获取小票样式
                  _self.getReceiptSet();

                  // 安全地设置全局变量
                  if (typeof global !== "undefined") {
                    _self.loginModify = global.login;
                    _self.modifyEmployeeStatus = global.flag;
                  }

                  // 应用节流处理滚动事件
                  _self.handleScroll = _self.throttle(_self.handleScroll, 100);

                  // 初始化窗口大小监听器
                  _self.initResizeListener();

                  // 添加滚动监听器
                  setTimeout(() => {
                    if (_self.$refs.scrollBox && _self.handleScroll) {
                      _self.$refs.scrollBox.addEventListener(
                        "scroll",
                        _self.handleScroll
                      );
                    }
                  }, 100);
                } catch (error) {
                  console.error("nextTick error:", error);
                }
              });
            } catch (error) {
              console.error("delayed mount error:", error);
            }
          }, 150); // 延迟150ms，避免与按钮动画冲突
        } catch (error) {
          console.error("mounted error:", error);
          // 确保即使出错也能正常显示组件
          this.isLoading = false;
        }
      },

      methods: {
        // 全局错误处理
        handleError: function (error) {
          console.error("Pay component error:", error);
          try {
            if (this.$message) {
              this.$message({
                type: "error",
                message: "支付组件发生错误，请重试",
                duration: 2000,
              });
            }
          } catch (e) {
            console.error("Error showing message:", e);
          }
        },

        // 安全的数组操作包装器
        safeArrayOperation: function (array, operation, ...args) {
          try {
            if (!Array.isArray(array)) {
              console.warn("safeArrayOperation: 不是有效数组");
              return null;
            }
            return operation.apply(array, args);
          } catch (error) {
            console.error("safeArrayOperation 出错:", error);
            return null;
          }
        },

        // 安全的属性访问
        safeGetProperty: function (obj, path, defaultValue = null) {
          try {
            if (!obj || typeof obj !== "object") {
              return defaultValue;
            }
            const keys = path.split(".");
            let current = obj;
            for (const key of keys) {
              if (
                current === null ||
                current === undefined ||
                !(key in current)
              ) {
                return defaultValue;
              }
              current = current[key];
            }
            return current;
          } catch (error) {
            console.error("safeGetProperty 出错:", error);
            return defaultValue;
          }
        },

        // 安全的DOM操作
        safeDOMOperation: function (element, operation, ...args) {
          try {
            if (!element || typeof element[operation] !== "function") {
              console.warn(`safeDOMOperation: 元素或方法 ${operation} 不存在`);
              return null;
            }
            return element[operation](...args);
          } catch (error) {
            console.error(`safeDOMOperation ${operation} 出错:`, error);
            return null;
          }
        },

        moveToBody: function () {
          try {
            // 确保DOM已经渲染完成
            this.$nextTick(() => {
              try {
                if (this.$refs.payWrap && document.body) {
                  // 检查元素是否已经在body下，避免重复添加
                  if (this.$refs.payWrap.parentNode !== document.body) {
                    document.body.appendChild(this.$refs.payWrap);
                  }

                  // 添加显示动画
                  setTimeout(() => {
                    if (this.$refs.payWrap) {
                      this.$refs.payWrap.style.opacity = "1";
                    }
                  }, 50);
                }
              } catch (error) {
                console.error("moveToBody nextTick error:", error);
              }
            });
          } catch (error) {
            console.error("moveToBody error:", error);
          }
        },

        removeFromBody: function () {
          try {
            // 组件销毁时，如果元素仍在body下，则移除
            if (this.$refs.payWrap && this.$refs.payWrap.parentNode) {
              if (this.$refs.payWrap.parentNode === document.body) {
                document.body.removeChild(this.$refs.payWrap);
              }
            }
          } catch (error) {
            console.error("removeFromBody error:", error);
          }
        },

        limitPrice() {
          this.isLimitPrice = !this.isLimitPrice;
          this.handleRefreshAmountList();
        },
        handleScrollToTitle_1() {
          if (this.$refs.scrollBox) {
            this.$refs.scrollBox.scrollTo({
              top: 0,
              behavior: "smooth",
            });
          }
        },
        handleScrollToTitle_2() {
          if (this.$refs.scrollBox && this.$refs.innerBox) {
            const innerBoxHeight = this.$refs.innerBox.offsetHeight || 0;

            this.$refs.scrollBox.scrollTo({
              top: innerBoxHeight,
              behavior: "smooth",
            });
          }
        },
        // 节流函数
        throttle: function (func, wait) {
          let lastTime = 0;
          return function (...args) {
            const now = Date.now();
            if (now - lastTime >= wait) {
              func.apply(this, args);
              lastTime = now;
            }
          };
        },

        handleScroll: function (event) {
          try {
            // 安全检查事件对象
            if (!event || !event.currentTarget) {
              console.warn("handleScroll: 无效的事件对象");
              return;
            }

            // 获取滚动容器
            const scrollBox = event.currentTarget;

            // 使用ref获取标题元素
            if (this.$refs && this.$refs.title2Element) {
              // 检查元素是否处于sticky状态
              // 通过检查元素的位置来确定是否已经"粘住"
              const rect = this.safeDOMOperation(
                this.$refs.title2Element,
                "getBoundingClientRect"
              );
              const scrollBoxRect = this.safeDOMOperation(
                scrollBox,
                "getBoundingClientRect"
              );

              if (rect && scrollBoxRect) {
                // 当元素的顶部与其应该粘住的位置（top值）相等时，它处于sticky状态
                const isSticky = rect.top <= scrollBoxRect.top + 62; // 60px是f-pay-title-1的高度

                // 根据sticky状态添加或移除类
                if (isSticky) {
                  this.safeDOMOperation(
                    this.$refs.title2Element.classList,
                    "add",
                    "is-sticky"
                  );
                } else {
                  this.safeDOMOperation(
                    this.$refs.title2Element.classList,
                    "remove",
                    "is-sticky"
                  );
                }
              }
            }
          } catch (error) {
            console.error("handleScroll error:", error);
          }
        },
        paymentBtnClick() {
          if (this.isLaka()) {
            console.log("isLaka");
            // 拉卡拉
            this.custmizePayTypeBill();
          } else if (this.isConsumeCards) {
            console.log("isConsumeCards", this.isConsumeCards);
            // 扣卡
            this.custmizePayTypeBill();
          } else if (this.isPay == 0) {
            // 会员余额
            console.log(this.isPay);
            this.payTheBill();
          } else if (this.isPay == 2) {
            // 现金
            console.log(this.isPay);
            this.payTheBill();
          } else {
            this.custmizePayTypeBill();
          }
        },
        handleRefreshAmountList() {
          this.paymentList = [];
          this.currentPage = 1;
          this.allCount = 0;
          this.getPaymentList();
        },
        handlePaymentClick(logNo) {
          if (this.linkPaymentId == logNo) {
            this.linkPaymentId = 0;
          } else {
            this.linkPaymentId = logNo;
          }
        },
        handleAmountChange() {
          this.paymentList = [];
          this.currentPage = 1;
          this.allCount = 0;
          this.getPaymentList();
        },
        getPaymentList() {
          const vm = this;
          vm.paymentLoading = true;
          const data = {
            storeid: vm.loginInfo.storeid,
            starttime: "",
            endtime: "",
            amount: this.isLimitPrice
              ? vm.custmizePaySearchForm.amount.toString()
              : null,
            limit: vm.limit,
            page: vm.currentPage,
          };
          if (vm.custmizePaySearchForm.date?.length == 2) {
            data.starttime = vm.custmizePaySearchForm.date[0];
            data.endtime = vm.custmizePaySearchForm.date[1];
          }

          $.ajax({
            url: vm.url + "/android/Payment/paymentList",
            type: "post",
            data,
            success: function (res) {
              console.log(res);
              if (res.code == 0) {
                vm.paymentList = res.data;
                vm.allCount = res.count;
              }
            },
            error: function (err) {
              console.log(err);
            },
            complete: function () {
              vm.paymentLoading = false;
            },
          });
        },
        handleSizeChange(val) {
          this.limit = val;
          this.currentPage = 1;
          this.getPaymentList();
        },
        handleCurrentChange(val) {
          this.currentPage = val;
          this.getPaymentList();
        },
        //修改下单时间
        handleModifyOrderTime: function () {
          this.manualOrderTime = this.kd_xinxi_list.order_time;
          this.isEditOrderTime = true;
        },
        //监听键盘
        manualPrice: function () {
          this.cz_shou_qian = this.cz_shou_qian.replace(/[^\d\.]/g, "");
          this.cz_shou_qian = this.cz_shou_qian.replace(/^\./g, "");
          this.cz_shou_qian = this.cz_shou_qian.replace(/\.{2,}/g, ".");
          this.cz_shou_qian = this.cz_shou_qian
            .replace(".", "$#$")
            .replace(/\./g, "")
            .replace("$#$", ".");
          this.cz_shou_qian = this.cz_shou_qian.replace(
            /^(\-)*(\d+)\.(\d\d).*$/,
            "$1$2.$3"
          );
          this.cz_shou_qian = this.cz_shou_qian.replace(/^0.$/, "0.");
          if (this.cz_shou_qian > 100000.0) {
            this.cz_shou_qian = "100000.00";
          }
        },

        //欠款
        toDebt: function () {
          if (this.sellCardType == 1) {
            this.debtForm.payMoney =
              this.debtForm.payMoney || this.cz_shou_qian;
          } else {
            this.debtForm.payMoney = this.cz_shou_qian;
          }
          this.isDebt = true;
        },

        //欠款去支付关闭弹框事件
        cancelDebt: function () {
          this.isDebt = false;
          this.unChangedCash = this.debtForm.payMoney;
        },

        //欠款input框事件
        handleDebtMoney: function (e) {
          try {
            // 安全检查事件对象
            if (!e || !e.target) {
              console.warn("handleDebtMoney: 无效的事件对象");
              return;
            }

            let debtPayMoney = this.debtForm.payMoney;
            if (typeof debtPayMoney !== "string") {
              debtPayMoney = String(debtPayMoney || "");
            }

            debtPayMoney = debtPayMoney.replace(/[^\d\.]/g, "");
            debtPayMoney = debtPayMoney.replace(/^\./g, "");
            debtPayMoney = debtPayMoney.replace(/\.{2,}/g, ".");
            debtPayMoney = debtPayMoney
              .replace(".", "$#$")
              .replace(/\./g, "")
              .replace("$#$", ".");
            debtPayMoney = debtPayMoney.replace(
              /^(\-)*(\d+)\.(\d\d).*$/,
              "$1$2.$3"
            );
            debtPayMoney = debtPayMoney.replace(/^0.$/, "0.");

            const reg = /^\d{0,8}\.{0,1}(\d{1,2})?/g;
            let payMoneyArr = debtPayMoney.match(reg);
            let payMoney = payMoneyArr && payMoneyArr[0] ? payMoneyArr[0] : 0;

            // 安全的数值转换
            const payMoneyNum = parseFloat(payMoney);
            if (isNaN(payMoneyNum) || !isFinite(payMoneyNum)) {
              console.warn("handleDebtMoney: 无效的数值");
              return;
            }

            //将输入框的值改要支付多少钱
            this.cz_shou_qian =
              Math.round(Number(payMoney) * 100) > this.debtForm.orderMoney
                ? (this.debtForm.orderMoney / 100).toFixed(2)
                : payMoney;
            this.cash =
              Math.round(Number(payMoney) * 100) > this.debtForm.orderMoney
                ? this.debtForm.orderMoney
                : Math.round(Number(payMoney) * 100);
            if (this.cash == this.debtForm.orderMoney) {
              this.isDebtMoney = false;
            } else {
              this.debtFlag = true;
              this.isDebtMoney = true;
            }
            if (payMoney) {
              let money = Math.round(Number(payMoney) * 100);
              this.debtForm.debtMoney =
                this.debtForm.orderMoney > money
                  ? this.debtForm.orderMoney - money
                  : 0;
              if (Number(e.target.value) === this.debtForm.orderMoney / 100) {
                this.debtForm.payMoney = e.target.value;
                return false;
              }
              this.debtForm.payMoney =
                this.debtForm.orderMoney > money
                  ? debtPayMoney
                  : (this.debtForm.orderMoney / 100).toFixed(2);
            } else {
              this.debtForm.debtMoney = this.debtForm.orderMoney;
            }
          } catch (error) {
            console.error("handleDebtMoney error:", error);
            this.handleError(error);
          }
        },

        //计算展示价格的值
        deleteDiscountBalance: function () {
          var _self = this;

          _self.orderDetails.orderInfo.forEach((item) => {
            if (item.consumeCard == 1) {
              _self.showMoney = _self.showMoney + item.smallTotal;
            }
          });

          _self.showMoneys = (_self.showMoney / 100).toFixed(2);
        },

        inputFocus: function (refName) {
          this.$nextTick(() => {
            if (this.$refs[refName]) {
              this.$refs[refName].focus();
            }
          });
        },
        bindPaymentOffer: function (item, index) {
          var _self = this;

          try {
            // 安全检查参数
            if (!item || typeof item !== "object") {
              console.warn("bindPaymentOffer: 无效的item参数");
              return;
            }
          } catch (error) {
            console.error("bindPaymentOffer error:", error);
            return;
          }
          //定义一个变量来记录点击的折扣与待支付相见的结果
          var dealPrice = 0;
          var dealPrice1 = 0;

          // 安全检查数组
          if (!Array.isArray(_self.selectedCard)) {
            _self.selectedCard = [];
          }
          if (!Array.isArray(_self.payCardInfo)) {
            _self.payCardInfo = [];
          }

          var selectCardLength = _self.selectedCard.length;
          //当选中一张卡，会加入moneydiscount表示这张卡抵扣了多少钱。
          if (selectCardLength == 0 && _self.showMoney == 0) {
            //来到收款页面，选择余额。所选的都已经是折扣或者抵扣、待支付直接为0.所欲不可以点击可选卡
            _self.SelectPreferenceSequence = -1;
          } else {
            if (_self.showMoney == 0 && selectCardLength != 0) {
              //当前已经选过了权益卡并且现在的待支付为0
              //此时点击其他的卡不会有什么变化，只能点击已经选择卡，取消已经选的权益。不管已经选了几个权益卡。最多也只能选俩个。
              if (selectCardLength == 1) {
                // 安全检查数组元素
                if (
                  _self.selectedCard[0] &&
                  _self.selectedCard[0]["id"] == item["id"]
                ) {
                  //现在待支付是0，且选中了一个但是要取消。
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (
                      _self.payCardInfo[i] &&
                      item["id"] == _self.payCardInfo[i]["id"]
                    ) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"] || 0;
                      _self.showMoney =
                        _self.selectedCard[0]["moneydiscount"] || 0;
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                } else {
                }
              } else {
                if (_self.selectedCard[0]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (
                      _self.selectedCard[0]["id"] == _self.payCardInfo[i]["id"]
                    ) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice =
                          _self.payCardInfo[i]["residuebalance"] -
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                    }
                  }
                } else if (_self.selectedCard[1]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(1, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (
                      _self.selectedCard[0]["id"] == _self.payCardInfo[i]["id"]
                    ) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice =
                          _self.payCardInfo[i]["residuebalance"] -
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        _self.selectedCard[0]["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                    }
                  }
                }
              }
            } else if (_self.showMoney != 0 && selectCardLength == 0) {
              for (let i = 0; i < _self.payCardInfo.length; i++) {
                if (_self.payCardInfo[i]["id"] == item["id"]) {
                  if (
                    _self.payCardInfo[i]["residuebalance"] >= _self.showMoney
                  ) {
                    _self.payCardInfo[i]["residuebalance"] -= _self.showMoney;
                    item["moneydiscount"] = _self.showMoney;
                    _self.showMoney = 0;
                    _self.showMoneys = _self.showMoney;
                  } else {
                    dealPrice = _self.payCardInfo[i]["residuebalance"] -=
                      _self.showMoney;
                    _self.payCardInfo[i]["residuebalance"] = 0;
                    item["moneydiscount"] +=
                      _self.payCardInfo[i]["residuebalance"];
                    _self.showMoney = Math.abs(dealPrice);
                    _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                  }
                  break;
                }
              }
              _self.selectedCard.push(item);
              _self.SelectPreferenceSequence = index;
            } else if (_self.showMoney != 0 && selectCardLength != 0) {
              if (selectCardLength == 1) {
                if (item["id"] == _self.selectedCard[0]["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney += _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                    }
                  }
                } else {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      if (
                        _self.payCardInfo[i]["residuebalance"] >=
                        _self.showMoney
                      ) {
                        _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        item["moneydiscount"] = _self.showMoney;
                        _self.showMoney = 0;
                        _self.showMoneys = _self.showMoney;
                      } else {
                        dealPrice = _self.payCardInfo[i]["residuebalance"] -=
                          _self.showMoney;
                        _self.payCardInfo[i]["residuebalance"] = 0;
                        item["moneydiscount"] +=
                          _self.payCardInfo[i]["residuebalance"];
                        _self.showMoney = Math.abs(dealPrice);
                        _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      }
                      break;
                    }
                  }
                  _self.selectedCard.push(item);
                  _self.SelectPreferenceSequence = index;
                }
              } else {
                //待支付不为0 且长度为2

                if (_self.selectedCard[0]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(0, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                } else if (_self.selectedCard[1]["id"] == item["id"]) {
                  for (let i = 0; i < _self.payCardInfo.length; i++) {
                    if (item["id"] == _self.payCardInfo[i]["id"]) {
                      _self.payCardInfo[i]["residuebalance"] +=
                        _self.selectedCard[0]["moneydiscount"];
                      _self.showMoney = _self.selectedCard[0]["moneydiscount"];
                      _self.showMoneys = (_self.showMoney / 100).toFixed(2);
                      _self.selectedCard.splice(1, 1);
                      _self.SelectPreferenceSequence = -1;

                      break;
                    }
                  }
                }
              }
            }
          }
          _self.cz_shou_qians = _self.showMoneys;
        },

        bindPayCard: function (item, index) {
          let _self = this;
          // console.log(item);
          // console.log(index);
          let cardPrice = Number(this.cz_shou_qian) * 100;

          if (_self.cardPayCardInfo.length >= 0) {
            let flag = false;
            let flagId = 0;
            for (let i = 0; i < _self.cardPayCardInfo.length; i++) {
              let payCardInfoItem = _self.cardPayCardInfo[i];
              if (item.id == payCardInfoItem.id) {
                flagId = i;
                flag = true;
                break;
              }
            }
            if (flag) {
              cardPrice = cardPrice + _self.cardPayCardInfo[flagId].cardPrice;
              _self.cardPayCardInfo.splice(flagId, 1);
              this.cash = cardPrice;
              this.cz_shou_qian = (cardPrice / 100).toFixed(2);
              // item.checked=false;
              for (let j = 0; j < _self.payCardInfo.length; j++) {
                let item1 = _self.payCardInfo[j];
                if (item.id == item1.id) {
                  item1.checked = false;
                }
              }
              _self.$forceUpdate();
              return false;
            }

            // console.log(_self.cz_shou_qian)
            if (item.residuebalance > 0 && cardPrice > 0) {
              item.checked = true;
              item.cardPrice =
                item.residuebalance >= cardPrice
                  ? cardPrice
                  : item.residuebalance;
              cardPrice =
                item.residuebalance >= cardPrice
                  ? 0
                  : cardPrice - item.residuebalance;
              this.cz_shou_qian = (cardPrice / 100).toFixed(2);
              this.cash = cardPrice;
              this.unChangedCash = this.cz_shou_qian;
              this.debtForm.orderMoney = this.cash;
              _self.cardPayCardInfo.push(item);
            } else {
              if (item.residuebalance == 0) {
                _self.$message({
                  message: "该卡余额不足，请充值",
                  type: "warning",
                  duration: 1500,
                });
              } else {
                _self.$message({
                  message: "已选择的卡足够支付，请确认付款",
                  type: "warning",
                  duration: 1500,
                });
              }
            }
          }
        },

        paymentChecked: function (id) {
          for (var i = 0; i < this.checkedOffer.length; i++) {
            if (this.checkedOffer[i].id == id) {
              return true;
            }
          }
          return false;
        },

        //获取自定义记账方式
        getCustomizePayType: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Pay/getCustomizePayType",
            type: "POST",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.customizePayTypeList = res.data;
                _self.chooseCustmizePayType(res.data[0].payType);
              }
            },
            error: function (err) {},
            complete: function () {
              // 补丁，保证 custmizePayType 值改变了，划卡才能正确
              _self.isLoading = false;
            },
          });
        },

        isLaka: function () {
          const nameItem = this.customizePayTypeList.find(
            (item) => item.name == "拉卡拉"
          );
          return nameItem?.payType == this.custmizePayType;
        },

        //选择自定义记账方式
        chooseCustmizePayType: function (payType) {
          this.bindPay(4);
          this.custmizePayType = payType;
          this.paymentBtn.text = "收款";
          if (this.isLaka()) {
            this.paymentBtn.width = "250px";
            this.paymentBtn.height = "56px";
            this.paymentBtn.right = "70px";
            this.paymentBtn.bottom = "36px";
            this.paymentBtn.vertical = false;
          } else {
            // 动态计算居中bottom、right;
            this.paymentBtn.width = "200px";
            this.paymentBtn.height = "102px";
            this.paymentBtn.vertical = true;
            this.centerPaymentBtn();
          }
        },

        //自定义支付
        custmizePayTypeBill: function () {
          if (this.isLoading) {
            return;
          }
          // debugger;
          let _self = this;
          console.log("🚀 ~ _self.requisiteCard:", _self.requisiteCard);
          var selectOffer = _self.selectedCard.concat(_self.requisiteCard);
          var arr = [];

          if (this.custmizePayType == 0) {
            _self.$message({
              type: "warning",
              message: "请选择记账方式",
              duration: 1500,
            });
            return false;
          }

          if (this.isPay == 0) {
            // 会员余额支付
            for (var i = 0; i < selectOffer.length; i++) {
              arr.push({
                cardId: selectOffer[i].id,
                money: selectOffer[i].realPay || selectOffer[i].moneydiscount,
              });
            }
          }

          if (
            _self.kd_xinxi_list.needVipPass &&
            _self.kd_xinxi_list.smallMoney < _self.kd_xinxi_list.vipWorth
          ) {
            _self.vipComsumeConfirm = true;
            return false;
          }
          if (_self.cardPayCardInfo.length > 0) {
            if (
              _self.kd_xinxi_list.smallMoney >= 0 &&
              _self.kd_xinxi_list.smallMoney < _self.kd_xinxi_list.toBePay
            ) {
              _self.vipComsumeConfirm = true;
              return false;
            }
          }
          let debt = 0;
          let debtMoney = 0;
          if (_self.debtFlag) {
            if (
              parseFloat(_self.debtForm.payMoney) * 100 ===
              _self.debtForm.orderMoney
            ) {
              debt = 0;
              debtMoney = 0;
            } else {
              debt = 1;
              debtMoney = _self.debtForm.debtMoney;
            }
          }

          /* const loading = this.$loading({
            lock: true,
            text: "支付...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          }); */
          _self.isPaying = true;

          const data = {
            smallChangeMoney: _self.smallChangeMoney,
            cardArr: JSON.stringify(arr),
            code: _self.paymentCode,
            debug: 0,
            orderNo: _self.kd_xinxi_list.order_number,
            payInfo: "",
            payType: _self.custmizePayType,
            returnType: _self.returnType,
            vipCode: "",
            vipPass: 0,
            merchantid: _self.userInfo.merchantid,
            storeid: _self.userInfo.storeid,
            cashier_id: _self.userInfo.id,
            shift_no: _self.userInfo.shift_no,
            debt: debt,
            debtMoney: debtMoney,
          };
          if (_self.linkPaymentId) {
            data["ctrade_no"] = _self.linkPaymentId;
          }
          if (this.manualOrderTime) {
            // 格式化时间
            data["cdate"] = this.manualOrderTime;
          }
          $.ajax({
            url: _self.url + "/android/pay/pay",
            type: "post",
            data,
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                // _self.loading = false;
                /* _self.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                  onClose: function () {
                    _self.endPay = 1;
                    _self.kd_xinxi_list.state = 4;
                  },
                }); */
                // _self.getOrderDetails();
                // _self.$forceUpdate();
                // loading.close();
                _self.isPaying = false;

                _self.endPay = 1;
                _self.kd_xinxi_list.state = 4;
              } else {
                // _self.loading = false;
                // loading.close();
                _self.isPaying = false;
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        handlePayBtnClick: function (payBtnType, payType) {
          // 解冻拉卡拉外的按钮
          const thawBill = () => {
            this.isLakalaSelected = false;
            setTimeout(() => {
              this.isShowLakalaDialog = false;
              this.payWrapInnerLeft = 160;
            }, 100);
          };

          if (payBtnType === "卡项划扣") {
            if (this.isLakalaSelected) {
              thawBill();
            } else {
              this.custmizePayTypeBill();
            }
          } else {
            // 重置不 会员余额 支付
            const resetSetNotBalancePayment = () => {
              this.cash = this.kd_xinxi_list.toBePay;
              this.cz_shou_qian = String(this.cash / 100);
              this.unChangedCash = String(this.cash / 100);
              this.debtForm.orderMoney = this.cash;
            };
            switch (payBtnType) {
              case "会员余额":
                if (this.isLakalaSelected) {
                  thawBill();
                } else {
                  // 如果余额不足，直接返回禁用状态
                  if (
                    this.payCardInfo[0].residuebalance <
                    this.kd_xinxi_list.toBePay
                  ) {
                    return;
                  }

                  this.isPay = 0;
                  // this.bindPay(0);
                  this.cz_shou_qian = "0";
                  this.cash = 0;
                  this.custmizePayType = 0;
                  this.payTheBill();
                }
                break;
              /* case "微信/支付宝":
                  this.isPay = 1;
                  // this.bindPay(1);
                  resetSetNotBalancePayment();
                  this.$nextTick(function () {
                    this.inputFocus(this.$refs.paymentCode);
                  });
                  this.custmizePayType = 0;
                  break; */
              case "现金":
                if (this.isLakalaSelected) {
                  thawBill();
                } else {
                  this.isPay = 2;
                  // this.bindPay(2);
                  resetSetNotBalancePayment();
                  this.custmizePayType = 0;
                  this.payTheBill();
                }
                break;
              case "自定义记账":
                if (this.isLakalaSelected && payType != 11) {
                  // 如果是冻结状态，且不是拉卡拉，就解冻
                  thawBill();
                } else {
                  // 如果不是冻结状态，无论是否拉卡拉
                  this.isPay = 4;
                  resetSetNotBalancePayment();
                  // 默认填写
                  this.custmizePaySearchForm.amount = (this.cash / 100).toFixed(
                    2
                  );
                  // 今日
                  let today = new Date();
                  let start = new Date(
                    today.getFullYear(),
                    today.getMonth(),
                    today.getDate()
                  );
                  let end = new Date(
                    today.getFullYear(),
                    today.getMonth(),
                    today.getDate()
                  );
                  this.custmizePaySearchForm.date = [start, end];
                  this.custmizePayType = payType;
                  if (payType == 11) {
                    // 拉卡拉的需要再次确认
                    if (this.isLakalaSelected) {
                      // 拉卡拉的再次确认
                      this.custmizePayTypeBill();
                      thawBill();
                    } else {
                      this.isLakalaSelected = true;
                      this.payWrapInnerLeft = 0;
                      this.isShowLakalaDialog = true;
                    }
                  } else {
                    // 其他自定义记账直接支付
                    this.custmizePayTypeBill();
                  }
                }
                break;
            }
          }
        },

        bindPay: function (pay) {
          this.isPay = pay;
          // 重置不 会员余额 支付
          const resetSetNotBalancePayment = () => {
            this.cash = this.kd_xinxi_list.toBePay;
            this.cz_shou_qian = String(this.cash / 100);
            this.unChangedCash = String(this.cash / 100);
            this.debtForm.orderMoney = this.cash;
          };

          switch (pay) {
            case 1:
              // 微信/支付宝支付
              resetSetNotBalancePayment();
              this.$nextTick(function () {
                this.inputFocus("paymentCode");
              });
              this.custmizePayType = 0;
              break;
            case 2:
              // 现金支付
              this.paymentBtn.width = "200px";
              this.paymentBtn.height = "102px";
              // 动态计算居中bottom、right;
              this.centerPaymentBtn();

              resetSetNotBalancePayment();
              /* this.$nextTick(function () {
                this.inputFocus(this.$refs.actualHarvest);
              }); */
              this.custmizePayType = 0;
              break;
            case 4:
              // 自定义记账
              resetSetNotBalancePayment();
              // 默认填写
              this.custmizePaySearchForm.amount = (this.cash / 100).toFixed(2);
              // 今日
              let today = new Date();
              let start = new Date(
                today.getFullYear(),
                today.getMonth(),
                today.getDate()
              );
              let end = new Date(
                today.getFullYear(),
                today.getMonth(),
                today.getDate()
              );
              this.custmizePaySearchForm.date = [start, end];
              // this.getCustomizePayType();
              break;
            default:
              // 会员余额支付
              this.cz_shou_qian = "0";
              this.cash = 0;
              this.custmizePayType = 0;
          }
        },

        // 获取订单内容
        fetchOrderDetail: function (fn, num) {
          var _self = this;

          try {
            _self.getOrderDetailsLoading = true;
            let orderNumber = _self.orderNumber;
            if (num) {
              orderNumber = orderNumber + "_print";
            }

            $.ajax({
              url: _self.url + "/android/order/getOrderDetail",
              type: "post",
              timeout: 10000, // 添加超时设置
              data: {
                merchantid: _self.userInfo.merchantid,
                orderNo: orderNumber,
                storeid: _self.userInfo.storeid,
                is_print: num ? num : 0,
              },
              success: function (res) {
                try {
                  console.log("🚀 ~ res:", res);
                  if (res && res.code == 1) {
                    // _self.isPay = 2;
                    _self.kd_xinxi_list = res.data;
                    _self.isRecharge = res.data.type === 4;
                    _self.orderId = res.data.id;
                    _self.kd_xinxi_list_buyer = res.data.buyer;
                    _self.kd_xinxi_list_cashierInfo = res.data.cashierInfo;
                    _self.cash = res.data.toBePay;
                    //为收银台套餐卡判断是不是次卡
                    _self.sellCardType = res.data.sellCardType;
                    //订单号详情
                    // _self.orderDetails = res.data.orderDetails;
                    _self.requisiteCard = res.data.requisiteCard;
                    // console.log(res.data.orderDetails,'订单详情');
                    // console.log(res.data.payTime,'支付时间');
                    if (res.data.toBePay < 0 && res.data.net_receipts > 0) {
                      // 定金退还
                      _self.isPay = 3;
                    }

                    // _self.orderDetails.forEach(item=>{
                    //     if(item.consumeCard==1){
                    //         _self.cash=_self.cash+item.smallTotal;

                    //     }
                    // })
                    // if(_self.kd_xinxi_list.member_counpon_money!=0 && _self.kd_xinxi_list.member_coupon!=0){
                    //     _self.cash=_self.cash-_self.kd_xinxi_list.member_counpon_money;
                    // }

                    //billToPay 1：充次卡
                    // if(_self.$props.billToPay==1){
                    //     if(_self.composeMoney<(_self.cash/100)){
                    //         _self.discountComposeMoney=_self.cash-(_self.composeMoney*100);
                    //         _self.cash=_self.composeMoney*100;
                    //     }
                    // }
                    // if(_self.cash!=0)

                    // 余额抵扣计算，还需额外支付多少现金cash
                    if (_self.requisiteCard.length != 0) {
                      for (let i = 0; i < _self.requisiteCard.length; i++) {
                        let item = _self.requisiteCard[i];
                        if (item.equityType == 2) {
                          _self.cash = _self.cash - item.realPay;
                        }
                      }
                    }
                    // console.log('cash的价格。。。。。。。。。。。。。。')
                    // console.log(_self.cash)
                    //现金那个页面的input框中的金额，刨除使用耗卡的付的钱
                    _self.cz_shou_qian = String(_self.cash / 100);
                    _self.unChangedCash = String(_self.cash / 100);
                    _self.debtForm.orderMoney = _self.cash;
                    //会员已有卡的全部信息
                    _self.payCardInfo = res.data.payCardInfo;
                    _self.payCardInfo.forEach((item) => {
                      item.checked = false;
                    });
                    _self.requisiteCard = res.data.requisiteCard;
                    // if (_self.cz_qudan) {
                    //     _self.$nextTick(function () {
                    //         _self.$refs.actualHarvest.focus()
                    //     }.bind(this))
                    // }
                    _self.getOrderDetails(fn);
                  } else {
                    console.error("fetchOrderDetail failed:", res);
                    if (_self.$message) {
                      _self.$message({
                        message: res && res.msg ? res.msg : "获取订单详情失败",
                        type: "warning",
                        duration: 1500,
                      });
                    }
                  }
                } catch (error) {
                  console.error(
                    "fetchOrderDetail success handler error:",
                    error
                  );
                }
              },
              error: function (xhr, status, error) {
                console.error("fetchOrderDetail ajax error:", error, status);
                if (_self.$message) {
                  _self.$message({
                    message: "网络请求失败，请重试",
                    type: "error",
                    duration: 1500,
                  });
                }
              },
              complete: function () {
                _self.getOrderDetailsLoading = false;
              },
            });
          } catch (error) {
            console.error("fetchOrderDetail error:", error);
            _self.getOrderDetailsLoading = false;
          }
        },
        //获取订单详情
        getOrderDetails: function (fn) {
          var _self = this;
          _self.orderListLoading = true;
          $.ajax({
            url: _self.url + "/android/Orderlist/OrderDetails",
            type: "POST",
            data: {
              id: _self.orderId,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                // console.log('--------------');
                // console.log(res.data.collection_time,'支付时间');
                console.log("kd_xinxi_list_buyer", _self.kd_xinxi_list_buyer);
                // console.log(res.data,'订单详情');
                try {
                  if (
                    _self.kd_xinxi_list_buyer &&
                    _self.kd_xinxi_list_buyer.id
                  ) {
                    let obj = { ..._self.kd_xinxi_list_buyer };
                    _self.kd_xinxi_list_buyer = {};
                    _self.kd_xinxi_list_buyer = {
                      ...obj,
                      payCardInfo: res.data.vip["payCardInfo"],
                    };
                  }
                } catch (e) {}
                _self.kd_xinxi_list.collection_time = res.data.collection_time;

                // 使用setTimeout确保DOM更新后再应用动画
                setTimeout(() => {
                  _self.orderDetails = res.data;
                  typeof fn === "function" && fn();
                }, 50);
              }
            },
            error: function (err) {},
            complete: function () {
              _self.orderListLoading = false;
            },
          });
        },

        //微信支付宝付款
        /*         phonePay() {
          if (
            this.kd_xinxi_list.needVipPass &&
            this.kd_xinxi_list.smallMoney < this.kd_xinxi_list.vipWorth
          ) {
            this.vipComsumeConfirm = true;
          } else {
            this.pay(0);
          }
        }, */

        pay: function (vipPass) {
          var _self = this;

          var payType = 0; //支付类型 1微信 2支付宝 3现金 4小程序微信 5会员余额
          if (this.isPay == 0) {
            payType = 5;
          } else if (this.isPay == 1) {
            payType = 1;
          } else if (this.isPay == 2) {
            payType = 3;
          } else if (this.isPay == 4) {
            payType = _self.custmizePayType;
          }

          // 安全检查数组
          if (!Array.isArray(_self.selectedCard)) {
            _self.selectedCard = [];
          }
          if (!Array.isArray(_self.requisiteCard)) {
            _self.requisiteCard = [];
          }
          if (!Array.isArray(_self.cardPayCardInfo)) {
            _self.cardPayCardInfo = [];
          }

          var selectOffer = _self.selectedCard.concat(_self.requisiteCard);
          var arr = [];

          if (_self.isPay == 0) {
            // 会员余额支付
            for (var i = 0; i < selectOffer.length; i++) {
              if (selectOffer[i] && typeof selectOffer[i] === "object") {
                arr.push({
                  cardId: selectOffer[i].id,
                  money:
                    selectOffer[i].realPay || selectOffer[i].moneydiscount || 0,
                });
              }
            }

            if (_self.sellCardType == 1) {
              for (let i = 0; i < _self.cardPayCardInfo.length; i++) {
                let item = _self.cardPayCardInfo[i];
                if (item && typeof item === "object") {
                  arr.push({
                    cardId: item.id,
                    money: item.cardPrice || 0,
                  });
                }
              }
            }
          }

          let debt = 0;
          let debtMoney = 0;
          if (_self.debtFlag) {
            if (
              parseFloat(_self.debtForm.payMoney) * 100 ===
              _self.debtForm.orderMoney
            ) {
              debt = 0;
              debtMoney = 0;
            } else {
              debt = 1;
              debtMoney = _self.debtForm.debtMoney;
            }
          }

          /* const loading = this.$loading({
            lock: true,
            text: "支付...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          }); */
          _self.isPaying = true;
          let vipCode = "";
          if (vipPass) {
            if (vipPass.length > 6) {
              vipCode = vipPass;
              vipPass = "";
            }
          }
          const data = {
            smallChangeMoney: _self.smallChangeMoney,
            cardArr: JSON.stringify(arr),
            code: _self.paymentCode,
            debug: 0,
            orderNo: _self.kd_xinxi_list.order_number,
            payInfo: "",
            payType: payType,
            returnType: _self.returnType,
            vipCode: vipCode,
            vipPass: vipPass,
            merchantid: _self.userInfo.merchantid,
            storeid: _self.userInfo.storeid,
            cashier_id: _self.userInfo.id,
            shift_no: _self.userInfo.shift_no,
            debt: debt,
            debtMoney: debtMoney,
          };
          if (this.manualOrderTime) {
            // 格式化时间
            data["cdate"] = this.manualOrderTime;
          }
          $.ajax({
            url: _self.url + "/android/pay/pay",
            type: "post",
            data,
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                // _self.loading = false;
                /* _self.$message({
                  type: "success",
                  message: res.msg,
                  duration: 1500,
                  onClose: function () {
                    _self.endPay = 1;
                    _self.kd_xinxi_list.state = 4;
                  },
                }); */
                // 为什么支付成功还要请求刷新订单数据？
                // _self.getOrderDetails();
                // _self.$forceUpdate();
                // loading.close();
                _self.isPaying = false;
                _self.vipComsumeConfirm = false;
                _self.vipPassComfirm = false;
                _self.vipPassword = "";
                _self.vipComsumeConfirm = false;
                _self.vipPassCodeComfirm = false;
                _self.vipPassCode = "";
                _self.debtFlag = false;

                _self.endPay = 1;
                _self.kd_xinxi_list.state = 4;
              } else {
                // _self.loading = false;
                // loading.close();
                _self.isPaying = false;
                _self.$message({
                  type: "error",
                  message: res.msg,
                  duration: 1500,
                });
              }
            },
          });
        },

        // 确认收款(收银支付)
        payTheBill: function () {
          // debugger;
          this.isDebt = false;
          var _self = this;
          console.log("_self.sellCardType", _self.sellCardType);
          if (_self.isPay == 0 || _self.isPay == 2) {
            // 0：余额支付； 2：现金支付
            // if ((_self.kd_xinxi_list.vip_id != 0 && (_self.requisiteCard.length > 0 || _self.selectedCard.length > 0)) || _self.kd_xinxi_list.deduction) {
            if (
              _self.kd_xinxi_list.needVipPass &&
              _self.kd_xinxi_list.smallMoney < _self.kd_xinxi_list.vipWorth
            ) {
              if (Number(this.cz_shou_qian) && this.isPay != 2) {
                this.$message({
                  type: "warning",
                  message: "订单未支付完成1",
                  duration: 3000,
                });
              } else {
                _self.vipComsumeConfirm = true;
              }
              // if (_self.isPay == 0 || _self.isPay == 2) {
              //     this.$prompt('', '会员消费确认', {
              //         confirmButtonText: '确定',
              //         cancelButtonText: '取消',
              //         inputPlaceholder: "输入您的会员密码",
              //         showClose: false,
              //         cancelButtonClass: "cancelButtonClass",
              //         inputType: 'password',
              //         closeOnClickModal: false,
              //         inputPattern: /^\d{6}$/,
              //         inputErrorMessage: '密码输入有误'
              //     }).then(({ value }) => {
              //         _self.pay(value)
              //     }).catch(() => {

              //     });
              // }
            } else if (_self.sellCardType == 1) {
              if (_self.cardPayCardInfo.length > 0) {
                if (_self.kd_xinxi_list.smallMoney >= 0) {
                  let money = 0;
                  for (let i = 0; i < _self.cardPayCardInfo.length; i++) {
                    let item = _self.cardPayCardInfo[i];
                    money = money + item.residuebalance;
                  }
                  if (money > _self.kd_xinxi_list.toBePay) {
                    money = _self.kd_xinxi_list.toBePay;
                  }
                  if (_self.kd_xinxi_list.smallMoney < money) {
                    if (Number(this.cz_shou_qian) && this.isPay != 2) {
                      this.$message({
                        type: "warning",
                        message: "订单未支付完成2",
                        duration: 1500,
                      });
                    } else {
                      _self.vipComsumeConfirm = true;
                    }
                  } else {
                    _self.pay();
                  }
                } else {
                  _self.pay();
                }
              } else {
                // 未知意义，暂时注释
                /* if (_self.isPay == 0) {
                  this.$message({
                    type: "warning",
                    message: "请选择卡，再完成付款",
                    duration: 1500,
                  });
                } else {
                  _self.pay();
                } */
                _self.pay();
              }
            } else {
              // let useMoney = this.$props.billToPay == 1 ? this.composeMoney : this.cz_shou_qian;
              if (Number(this.cz_shou_qian) && this.isPay != 2) {
                this.$message({
                  type: "warning",
                  message: "订单未支付完成3",
                  duration: 1500,
                });
              } else {
                if (_self.debtFlag) {
                  _self.pay("debtFlag");
                } else {
                  let useMoney = Number(this.cz_shou_qian);
                  let total = useMoney - Number(this.cash) / 100;
                  // debugger_test
                  if (!this.cz_shou_qian) {
                    this.$message({
                      type: "error",
                      message: "输入支付金额",
                      duration: 1500,
                    });
                    return false;
                  }
                  if (total < 0) {
                    this.$message({
                      type: "error",
                      message: "支付金额不足",
                      duration: 1500,
                    });
                    return false;
                  }
                  _self.pay();
                }
              }
            }
          } else if (_self.isPay == 1) {
            // 微信/支付宝支付
            if (!this.paymentCode) {
              this.$message({
                type: "error",
                message: "付款码不能为空",
                duration: 1500,
              });
              return false;
            }
            _self.pay();
          }
        },

        // 支付完成
        newBilling: function () {
          this.$emit("close-pay", false);
          this.clearData();
        },

        //新建会员开单
        newMemberBilling: function () {
          //window.location.href = "cashier_system.html?phone=" + this.kd_xinxi_list_buyer.phone + "&flag=3";
          let href =
            "cashier_system.html?phone=" +
            this.kd_xinxi_list_buyer.phone +
            "&flag=3";
          top.app.toPage(href);
        },

        //获取所有销售和服务人员
        getAllSales: function () {
          var _self = this;
          $.ajax({
            url: _self.url + "/android/Deduct/getStaff",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.AllSales = res.data;
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });
        },

        //获取单据业绩提成记录
        modifyEmployeePerformance: function () {
          var _self = this;
          this.isModifyPerformance = true;
          // console.log(_self.loginInfo.merchantid,_self.loginInfo.storeid,_self.kd_xinxi_list.order_number)
          this.loading = true;
          $.ajax({
            url: _self.url + "/android/Deduct/getOrderDeductData",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              orderNo: _self.kd_xinxi_list.order_number,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.zuhekaPerformance = res.data.type;
                _self.totalPerformance = res.data;
                _self.performanceList = res.data.performance;
                let length = _self.performanceList.length - 1;
                for (let i = 0; i < _self.performanceList.length; i++) {
                  _self.performanceList[i].salesChecked = [];
                  _self.performanceList[i].craftsChecked = [];
                  if (_self.performanceList.base_amount) {
                  } else {
                    _self.performanceList[i].base_amount =
                      _self.performanceList[length].base_amount;
                  }
                }
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });
          _self.getAllSales();
        },

        //选择销售
        chooseSales: function (performanceList, index) {
          var _self = this;
          this.isSales = true;
          this.isHandelIndex = index;
          _self.salesChecked =
            _self.performanceList[_self.isHandelIndex].salesChecked;
          for (let j = 0; j < _self.AllSales.length; j++) {
            Vue.delete(_self.AllSales[j], "isDisabled");
            for (
              let i = 0;
              i < _self.performanceList[_self.isHandelIndex].salesmen.length;
              i++
            ) {
              if (
                _self.performanceList[_self.isHandelIndex].salesmen[i]
                  .staff_id == _self.AllSales[j].id
              ) {
                _self.AllSales[j].isDisabled = 1;
              }
            }
          }
        },
        //添加销售
        addSalesmen: function () {
          var _self = this;
          this.isSales = false;
          let salesmenLength =
            _self.performanceList[_self.isHandelIndex].salesmen.length;
          // console.log(_self.salesChecked,"下标")
          _self.performanceList[_self.isHandelIndex].addSalesmen = []; //存储添加的销售
          _self.performanceList[_self.isHandelIndex].salesChecked =
            _self.salesChecked;
          for (let i = 0; i < _self.salesChecked.length; i++) {
            salesmenLength += 1;
            _self.performanceList[_self.isHandelIndex].addSalesmen[i] = {
              staffName: _self.AllSales[_self.salesChecked[i]].nickname,
              lengthh: salesmenLength,
              assign: 2,
              base_amount:
                _self.performanceList[_self.isHandelIndex].base_amount,
              commission: 0.0,
              commission_proportion: 0.0,
              performance: 0.0,
              performance_proportion: 0.0,
              deduct_type: 1,
              deduct_way: 1,
              order_time: _self.totalPerformance.order_time,
              staff_id: _self.AllSales[_self.salesChecked[i]].id,
              storeid: _self.totalPerformance.storeid,
              merchantid: _self.totalPerformance.merchantid,
              id: 0,
              order_id: _self.performanceList[_self.isHandelIndex].order_id,
              order_detail_id: _self.performanceList[_self.isHandelIndex].id,
              goods_type:
                _self.zuhekaPerformance == 5 ? 2 : _self.zuhekaPerformance,
            };
          }
        },

        //选择服务人员
        chooseCrafts: function (performanceList, index) {
          var _self = this;
          this.isCrafts = true;
          this.isHandelIndex = index;
          _self.AllCrafts = _self.AllSales.filter(function (items, index, ar) {
            if (items.isTech == 2) {
              return items;
            }
          });
          _self.craftsChecked =
            _self.performanceList[_self.isHandelIndex].craftsChecked;
          for (let j = 0; j < _self.AllCrafts.length; j++) {
            Vue.delete(_self.AllCrafts[j], "isDisabled");
            for (
              let i = 0;
              i < _self.performanceList[_self.isHandelIndex].technicians.length;
              i++
            ) {
              if (
                _self.performanceList[_self.isHandelIndex].technicians[i]
                  .staff_id == _self.AllCrafts[j].id
              ) {
                _self.AllCrafts[j].isDisabled = 1;
              }
            }
          }
        },
        //添加服务人员
        addCrafts: function () {
          var _self = this;
          this.isCrafts = false;
          let craftsLength =
            _self.performanceList[_self.isHandelIndex].technicians.length;
          _self.performanceList[_self.isHandelIndex].addCrafts = [];
          _self.performanceList[_self.isHandelIndex].craftsChecked =
            _self.craftsChecked;
          for (let i = 0; i < _self.craftsChecked.length; i++) {
            craftsLength += 1;
            _self.performanceList[_self.isHandelIndex].addCrafts[i] = {
              staffName: _self.AllCrafts[_self.craftsChecked[i]].nickname,
              lengthh: craftsLength,
              assign: 2,
              base_amount:
                _self.performanceList[_self.isHandelIndex].base_amount,
              commission: 0.0,
              commission_proportion: 0.0,
              performance: 0.0,
              performance_proportion: 0.0,
              deduct_type: 2,
              deduct_way: 1,
              order_time: _self.totalPerformance.order_time,
              staff_id: _self.AllCrafts[_self.craftsChecked[i]].id,
              storeid: _self.totalPerformance.storeid,
              merchantid: _self.totalPerformance.merchantid,
              id: 0,
              order_id: _self.performanceList[_self.isHandelIndex].order_id,
              order_detail_id: _self.performanceList[_self.isHandelIndex].id,
              goods_type:
                _self.zuhekaPerformance == 5 ? 2 : _self.zuhekaPerformance,
            };
          }
        },

        //选择提成方式
        chooseDeductType: function (e, sindex, lindex) {
          this.$forceUpdate();
        },
        //输入金额呈现百分比
        limitInputMoney: function (performanceIndex, lineIndex, value, type) {
          let per = (
            (value * 10000) /
            this.performanceList[performanceIndex].base_amount
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].performance_proportion = per.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].performance_proportion = per.toString();
          }
        },
        limitInputMoneyAdd: function (performanceIndex, items, index, type) {
          let proportion = (
            (parseInt(items.performance) * 10000) /
            items.base_amount
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].performance_proportion = proportion;
          } else {
            this.performanceList[performanceIndex].addCrafts[
              index
            ].performance_proportion = proportion;
          }
        },
        limitInputMoneyAdd1: function (performanceIndex, items, index, type) {
          let proportion = (
            (parseInt(items.commission) * 10000) /
            items.base_amount
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].commission_proportion = proportion;
          } else {
            this.performanceList[performanceIndex].addCrafts[
              index
            ].commission_proportion = proportion;
          }
        },
        limitInputMoney1: function (performanceIndex, lineIndex, value, type) {
          let per = (
            (value * 10000) /
            this.performanceList[performanceIndex].base_amount
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].commission_proportion = per.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].commission_proportion = per.toString();
          }
        },
        //输入百分比呈现金额
        limitInputPer: function (performanceIndex, lineIndex, value, type) {
          let money = (
            (value * this.performanceList[performanceIndex].base_amount) /
            10000
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].performance = money.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].performance = money.toString();
          }
        },
        limitInputPer1: function (performanceIndex, lineIndex, value, type) {
          let money = (
            (value * this.performanceList[performanceIndex].base_amount) /
            10000
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].salesmen[
              lineIndex
            ].commission = money.toString();
          } else {
            this.performanceList[performanceIndex].technicians[
              lineIndex
            ].commission = money.toString();
          }
        },
        limitInputPerAdd: function (performanceIndex, items, index, type) {
          let performance = (
            (parseInt(items.performance_proportion) * items.base_amount) /
            10000
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].performance = performance;
          } else {
            this.performanceList[performanceIndex].addCrafts[
              index
            ].performance = performance;
          }
        },
        limitInputPerAdd1: function (performanceIndex, items, index, type) {
          let commission = (
            (parseInt(items.commission_proportion) * items.base_amount) /
            10000
          ).toFixed(2);

          if (type === "sales") {
            this.performanceList[performanceIndex].addSalesmen[
              index
            ].commission = commission;
          } else {
            this.performanceList[performanceIndex].addCrafts[index].commission =
              commission;
          }
        },

        //删除销售、服务人员
        delectsalesmen: function (info, index, inde) {
          var _self = this;
          _self.allDelect.push(_self.performanceList[inde].salesmen[index]);
          _self.performanceList[inde].salesmen.splice(index, 1);
          this.$forceUpdate();
        },
        delectCrafts: function (info, index, inde) {
          var _self = this;
          _self.allDelect.push(_self.performanceList[inde].technicians[index]);
          _self.performanceList[inde].technicians.splice(index, 1);
          this.$forceUpdate();
        },

        //提交保存修改的数据
        saveModify: function () {
          var _self = this;
          _self.loading = true;
          _self.saveModifyArr = [];
          _self.delArr = [];
          _self.addArr = [];
          //遍历拼接数组
          for (let i = 0; i < _self.performanceList.length; i++) {
            _self.saveModifyArr = _self.saveModifyArr.concat(
              _self.performanceList[i].salesmen,
              _self.performanceList[i].technicians
            );
            if (_self.performanceList[i].addSalesmen) {
              if (_self.performanceList[i].addCrafts) {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addSalesmen,
                  _self.performanceList[i].addCrafts
                );
              } else {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addSalesmen
                );
              }
            } else {
              if (_self.performanceList[i].addCrafts) {
                _self.addArr = _self.addArr.concat(
                  _self.performanceList[i].addCrafts
                );
              } else {
                _self.addArr = _self.addArr;
              }
            }
          }
          //删除多余的属性
          for (let i = 0; i < _self.addArr.length; i++) {
            Vue.delete(_self.addArr[i], "lengthh");
          }
          _self.delArr = _self.allDelect;
          _self.saveModifyArr = JSON.stringify(_self.saveModifyArr);
          _self.delArr = JSON.stringify(_self.delArr);
          _self.addArr = JSON.stringify(_self.addArr);
          $.ajax({
            url: _self.url + "/android/Deduct/saveDeductData",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              orderNo: _self.kd_xinxi_list.order_number,
              storeid: _self.loginInfo.storeid,
              addArr: _self.addArr,
              delArr: _self.delArr,
              saveArr: _self.saveModifyArr,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.loading = false;
                _self.$message({
                  message: res.msg,
                  type: "success",
                  duration: 1500,
                });
                _self.isModifyPerformance = false;
              } else {
                _self.$message({
                  message: res.msg,
                  type: "warning",
                  duration: 1500,
                });
                _self.loading = false;
              }
            },
            error: function (error) {},
          });

          // console.log(_self.addArr)
        },

        // (清空页面数据)收款数据
        clearData: function () {
          this.kd_kaidanxinxi = []; // 收款开单信息
          this.kd_xinxi_list = {};
          this.kd_xinxi_list_buyer = {};
          this.kd_xinxi_list_cashierInfo = {}; //
          this.orderDetails = {};
          this.payCardInfo = [];
          this.paymentOffer = [];
          this.checkedOffer = [];
          this.cz_shou_qian = "";
          this.paymentCode = ""; //微信支付宝付款码
          this.vipPass = ""; //会员密码
          this.returnType = 3;
        },

        cz_qudan_close: function () {
          try {
            // 防止重复调用
            if (this._isClosing) {
              return;
            }
            this._isClosing = true;

            const dialogElement = this.$refs.payWrap;

            // 清理状态
            this.manualOrderTime = "";

            // 立即停止所有定时器和动画
            if (this.timerManager) {
              if (this.timerManager.clearAll) {
                this.timerManager.clearAll();
              } else if (this.timerManager.clearAllTimers) {
                this.timerManager.clearAllTimers();
              }
            }

            // 清理事件监听器
            try {
              const scrollBox = document.querySelector(".f-pay-scroll-box");
              if (scrollBox && this.handleScroll) {
                scrollBox.removeEventListener("scroll", this.handleScroll);
              }
            } catch (error) {
              console.warn("Error removing scroll listener:", error);
            }

            // 添加关闭动画
            if (dialogElement) {
              dialogElement.classList.remove("dialog-animation");
              dialogElement.classList.add("dialog-animation-close");

              // 使用requestAnimationFrame确保动画完成后再关闭
              const animationFrame = requestAnimationFrame(() => {
                setTimeout(() => {
                  try {
                    this._performClose();
                  } catch (error) {
                    console.error("Error in animation close:", error);
                    this._performClose();
                  }
                }, 400);
              });

              // 备用关闭机制，防止动画卡住
              setTimeout(() => {
                if (this._isClosing) {
                  cancelAnimationFrame(animationFrame);
                  this._performClose();
                }
              }, 600);
            } else {
              // 如果没有找到对话框元素，直接关闭
              this._performClose();
            }
          } catch (error) {
            console.error("cz_qudan_close error:", error);

            // 确保即使出错也能关闭对话框
            this._performClose();
          }
        },

        // 执行实际的关闭操作
        _performClose: function () {
          try {
            // 移除DOM元素
            this.removeFromBody();

            // 发送关闭事件
            this.$emit("close-pay", false);

            if (typeof global !== "undefined") {
              global.flag = 1;
            }

            // 重置关闭状态
            this._isClosing = false;
          } catch (error) {
            console.error("_performClose error:", error);

            // 强制重置状态
            this._isClosing = false;
          }
        },

        /**键盘**/
        cz_del_all: function () {
          this.cz_shou_qian = "";
          this.inputFocus("actualHarvest");
        },

        cz_del_one: function () {
          this.inputFocus("actualHarvest");
          let input_lenth = this.cz_shou_qian.length;
          if (this.cz_shou_qian.length > 0) {
            this.cz_shou_qian = this.cz_shou_qian.slice(0, input_lenth - 1);
          }
        },

        cz_input_num: function (index, value1) {
          this.inputFocus("actualHarvest");
          let isdian = this.cz_shou_qian.indexOf(".");
          //console.log('dian' + isdian);
          let input_lenth = this.cz_shou_qian.length;
          let get_hlnum = index + value1;
          let cha_num = input_lenth - isdian - 1;

          if (input_lenth == 0) {
            if (get_hlnum == "33" || get_hlnum == "31" || get_hlnum == "32") {
              this.cz_shou_qian += "0.";
            } else {
              for (let i of this.zj_all_num) {
                // //console.log(i.key);
                if (i.key == get_hlnum) {
                  this.cz_shou_qian += i.value;
                }
              }
            }
          } else {
            if (isdian == -1) {
              for (let i of this.zj_all_num) {
                if (i.key == get_hlnum) {
                  this.cz_shou_qian += i.value;
                }
              }
              if (this.cz_shou_qian > 100000.0) {
                this.cz_shou_qian = "100000.00";
                this.$message({
                  showClose: true,
                  message: "最大付款金额10万",
                  type: "warning",
                });
                let that = this;
                setTimeout(function () {
                  that.zj_max_price = true;
                }, 200);
                setTimeout(function () {
                  that.zj_max_price = false;
                }, 1200);
              }
            } else if (this.cz_shou_qian == "0.") {
              if (cha_num * 1 < 2) {
                for (let i of this.zj_all_num) {
                  if (!(get_hlnum == "33")) {
                    if (i.key == get_hlnum) {
                      this.cz_shou_qian += i.value;
                    }
                  }
                }
              }
            } else {
              if (cha_num < 2) {
                for (let i of this.zj_all_num) {
                  // //console.log(i.key);
                  if (i.key == get_hlnum) {
                    this.cz_shou_qian += i.value;
                  }
                }
              }
            }
          }
        },

        // 获取小票样式
        getReceiptSet: function () {
          var _self = this;

          $.ajax({
            url: _self.url + "/android/order/getReceiptSet",
            type: "post",
            data: {
              merchantid: _self.loginInfo.merchantid,
              storeid: _self.loginInfo.storeid,
            },
            success: function (res) {
              // var res = JSON.parse(res);
              if (res.code == 1) {
                _self.printSet = res.data;
                _self.paperwidth = res.data.width;
              } else {
                let data =
                  '{"id":1,"storeid":1,"merchantid":1,"name":"默认小票","status":1,"addtime":"2020-01-01 08:00","set":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}],"type":1,"width":180,"paperwidth":58,"setInfo":[{"name":"store","set":{"fontSize":20}},{"name":"header","set":{"fontSize":12,"text":"收银小票","headerfontSize":18,"ordertype":1,"ordernum":1,"ordertime":1,"num":1,"cashier":1}},{"name":"goods","set":{"fontSize":12,"goodsname":1,"price":1,"num":1,"allmoney":1,"sku":1}},{"name":"vip","set":{"fontSize":12,"name":1,"cardnum":1,"money":1,"score":1}},{"name":"takegoods","set":{"fontSize":12,"name":1,"phone":1,"address":1,"remark":1,"fee":1}},{"name":"footer","set":{"fontSize":12,"paytime":1,"ordernum":1,"barcode":1,"needpay":1,"getmoney":1,"paytype":1,"coupon":1,"smallchange":1,"returnmoney":1}},{"name":"text","set":{"text":"谢谢惠顾，欢迎再次光临！","fontSize":20}}]}';
                data = JSON.parse(data);
                _self.printSet = data;
                _self.paperwidth = data.width;
              }
            },
          });
        },

        bindPrint: function () {
          if (!LODOPbol) {
            this.noPrint();
            return;
          }
          var vm = this;
          this.printing = true;
          this.fetchOrderDetail(() => {
            this.$nextTick(() => {
              if (this.printSet.length == 0) {
                console.log("Preview1");
                Preview1();
              } else {
                // vm.printorderinfo = res.info;
                var str = vm.$refs.printorderstr.innerHTML;
                console.log("Preview2");
                Preview2(str);
              }
            });
          }, 1);
          this.timerManager.createTimer(
            "printingTimer",
            () => {
              this.printing = true;
            },
            3000
          );
        },
        // 没有安装打印机
        noPrint: function () {
          let self = this;
          self.$message({
            type: "error",
            message: "打印机未准备好,无法打印",
            duration: 1500,
            onClose: function () {
              LODOPbol = false;
            },
          });
        },
        //抹零
        returnZero: function (index) {
          let _self = this;
          if (index) {
            _self.cz_shou_qian = String(Number(_self.unChangedCash).toFixed(2));
            _self.cash = Number(_self.unChangedCash) * 100;
            _self.smallChangeMoney = 0;
            _self.isReturnZero = false;
          } else {
            if (_self.unChangedCash == _self.cz_shou_qian) {
              _self.formerMoney = Number(_self.cz_shou_qian);
              _self.formrCash = _self.cash;
            } else {
              _self.formerMoney = Number(_self.unChangedCash);
              _self.formrCash = Number(_self.unChangedCash);
            }
            let nowMoney = _self.formerMoney | 0;
            _self.smallChangeMoney =
              (_self.formerMoney * 100 - nowMoney * 100) | 0;
            _self.cz_shou_qian = String(nowMoney.toFixed(2));
            _self.cash = nowMoney * 100;
            _self.isReturnZero = true;
          }
        },

        // 防抖函数
        debounce: function (func, wait) {
          let timeout;
          return function executedFunction(...args) {
            const later = () => {
              clearTimeout(timeout);
              func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
          };
        },

        // 动态计算居中位置
        centerPaymentBtn: function () {
          // 如果是拉卡拉模式，不需要动态居中，保持固定位置
          if (this.isLaka()) {
            return;
          }

          this.$nextTick(() => {
            if (this.$refs.rightMain) {
              const parentWidth = this.$refs.rightMain.offsetWidth;
              const parentHeight = this.$refs.rightMain.offsetHeight;

              // 获取按钮尺寸
              const btnWidth = parseInt(this.paymentBtn.width);
              const btnHeight = parseInt(this.paymentBtn.height);

              // 计算居中位置
              const centerRight = (parentWidth - btnWidth) / 2;
              const centerBottom = (parentHeight - btnHeight) / 2;

              // 更新按钮位置
              this.paymentBtn.right = centerRight + "px";
              this.paymentBtn.bottom = centerBottom + "px";
            }
          });
        },

        // 初始化窗口大小监听
        initResizeListener: function () {
          if (!this.resizeHandler) {
            // 只使用防抖，用户停止调整窗口大小后300ms执行
            this.resizeHandler = this.debounce(this.centerPaymentBtn, 50);
            window.addEventListener("resize", this.resizeHandler);
          }
        },

        // 清理窗口大小监听
        cleanupResizeListener: function () {
          if (this.resizeHandler) {
            window.removeEventListener("resize", this.resizeHandler);
            this.resizeHandler = null;
          }
        },

        filterMoney: function (money) {
          try {
            const num = parseFloat(money);
            if (isNaN(num) || !isFinite(num)) {
              return "0.00";
            }
            return (num / 100).toFixed(2);
          } catch (error) {
            console.error("filterMoney error:", error);
            return "0.00";
          }
        },

        // 安全的数值计算
        safeCalculate: function (operation, ...args) {
          try {
            const result = operation(...args);
            if (isNaN(result) || !isFinite(result)) {
              console.warn("safeCalculate: 计算结果无效");
              return 0;
            }
            return result;
          } catch (error) {
            console.error("safeCalculate error:", error);
            return 0;
          }
        },

        // 强制清理资源
        forceCleanup: function () {
          try {
            console.log("执行强制资源清理");

            // 清理所有定时器
            if (this.timerManager && this.timerManager.clearAll) {
              this.timerManager.clearAll();
            }

            // 强制垃圾回收提示
            if (window.gc && typeof window.gc === "function") {
              window.gc();
            }
          } catch (error) {
            console.error("forceCleanup error:", error);
          }
        },
      },

      filters: {
        // 格式化充值金额/100
        /*         filterMoney: function (money) {
          return (money / 100).toFixed(2);
        }, */
        //格式化正负号
        formatMark: function (money) {
          if (money.indexOf("-") != -1) {
            money = money.split("-")[1];
          }
          return money;
        },
      },

      computed: {
        kd_shishou: function () {
          if (this.isReturnZero) {
            let useMoney = Number(this.cz_shou_qian);
            let total = useMoney - this.cash / 100;
            if (total > 0) {
              return "找零:" + total.toFixed(2) + "元";
            } else if (total < 0) {
              return "还缺:" + (-total).toFixed(2) + "元";
            } else {
              return "不用找零";
            }
          } else {
            let useMoney = this.cz_shou_qian;
            let total = useMoney - this.cash / 100;
            if (total > 0) {
              return "找零:" + total.toFixed(2) + "元";
            } else if (total < 0) {
              return "还缺:" + (-total).toFixed(2) + "元";
            } else {
              return "不用找零";
            }
          }
        },
        receivableing: function () {
          let money = 0;
          if (this.orderDetails && this.orderDetails.id) {
            if (this.orderDetails.dismoney != "0.00") {
              money =
                money + Math.round(Number(this.orderDetails.dismoney) * 100);
            }
            if (this.orderDetails.deduction != "0.00") {
              money =
                money + Math.round(Number(this.orderDetails.deduction) * 100);
            }
            if (this.orderDetails.member_counpon_money) {
              money = money + this.orderDetails.member_counpon_money;
            }
            if (this.orderDetails.manually) {
              money = money + this.orderDetails.manually;
            }
            if (this.orderDetails.small_change_money) {
              money = money + this.orderDetails.small_change_money;
            }
            money =
              money + Math.round(Number(this.orderDetails.receivable) * 100);
            if (this.orderDetails.dispatch_fee != "0.00") {
              money =
                money -
                Math.round(Number(this.orderDetails.dispatch_fee) * 100);
            }
            return money;
          } else {
            return 0;
          }
        },

        // toBePay: function () {
        //     let product = this.orderDetails;
        //     // 所有卡项
        //     let payCardInfo = this.payCardInfo;
        //     // 选择支付充值卡
        //     let offter = this.checkedOffer;
        //     // 支付必须使用卡项
        //     let requisiteCard = this.requisiteCard;
        //
        //     let money = 0;
        //     let offterMoney = 0;
        //     let payPrince = 0;
        //     // let allPay = 0;
        //
        //     // 计算卡的剩余金额
        //     // for (var i = 0; i < product.length; i++) {
        //     //     money += product[i].num * product[i].price - product[i].reduceprice;
        //     //     if (product[i].equity_type == 2 || product[i].equity_type == 3) {
        //     //         for (var j = 0; j < requisiteCard.length; j++) {
        //     //             if (product[i].card_id == requisiteCard[j].cardId) {
        //     //                 requisiteCard[j]['money'] = parseInt(requisiteCard[j].residuebalance - product[i].smallTotal);
        //     //                 offterMoney += requisiteCard[j]['money']
        //     //             }
        //     //         }
        //     //     }
        //     // }
        //     // console.log(`money ${money}`);
        //     // console.log(`offterMoney ${offterMoney}`);
        //     // let pendingPrice = 0;
        //     //
        //     // // allPay = money - offterMoney;
        //     // this.remaining = money - offterMoney;
        //     // console.log('展示的值', this.remaining);
        //     // if (this.remaining < 0) {
        //     //     // console.log(`remaining ${remaining}`);
        //     //     this.remaining = 0;
        //     // } else {
        //     //     for (var i = 0; i < offter.length; i++) {
        //     //         if (offter[i].id == this.selectId) {
        //     //             this.remaining -= offter[i].residuebalance;
        //     //         }
        //     //     }
        //     //
        //     // }
        //
        //     // return this.remaining
        // }
      },

      watch: {
        buyReceipt: {
          handler: function (n) {
            this.cz_qudan = n;
          },
          deep: true,
          immediate: true,
        },
        loginInfo: {
          handler: function (n) {
            this.userInfo = n;
          },
          deep: true,
          immediate: true,
        },
        orderNo: {
          handler: function (n) {
            this.orderNumber = n;
          },
          deep: true,
          immediate: true,
        },
        useCard: {
          handler: function (n) {
            this.isCard = n;
          },
          deep: true,
          immediate: true,
        },
        showMoneys: {
          handler: function (n) {
            this.cz_shou_qian = this.showMoneys;
          },
          deep: true,
          immediate: true,
        },
      },
      beforeDestroy: function () {
        try {
          console.log("pay component beforeDestroy - 开始清理资源");

          // 重置防重复标志
          this._isClosing = false;

          // 清理窗口大小监听器
          this.cleanupResizeListener();

          // 移除滚动监听器
          try {
            const scrollBox = document.querySelector(".f-pay-scroll-box");
            if (scrollBox && this.handleScroll) {
              scrollBox.removeEventListener("scroll", this.handleScroll);
            }
            // 清理引用
            this.handleScroll = null;
          } catch (error) {
            console.error("remove scroll listener error:", error);
          }

          // 清理所有管理的定时器
          try {
            if (this.timerManager) {
              if (this.timerManager.clearAll) {
                this.timerManager.clearAll();
              } else if (this.timerManager.clearAllTimers) {
                this.timerManager.clearAllTimers();
              }
            }
          } catch (error) {
            console.error("clear timers error:", error);
          }

          // 清理数组引用，防止内存泄漏
          try {
            const arrayProps = [
              "payCardInfo",
              "requisiteCard",
              "paymentOffer",
              "checkedOffer",
              "cz_chongzhika",
              "printSet",
              "selectedCard",
              "performanceList",
              "salesmenList",
              "techniciansList",
              "AllSales",
              "AllCrafts",
              "salesChecked",
              "craftsChecked",
              "allDelect",
              "saveModifyArr",
              "addArr",
              "delArr",
              "customizePayTypeList",
              "cardPayCardInfo",
              "paymentList",
            ];

            arrayProps.forEach((prop) => {
              if (Array.isArray(this[prop])) {
                this[prop].length = 0;
                this[prop] = null;
              }
            });

            // 清理对象引用
            const objectProps = [
              "kd_kaidanxinxi",
              "kd_xinxi_list",
              "kd_xinxi_list_buyer",
              "kd_xinxi_list_cashierInfo",
              "orderDetails",
              "userInfo",
              "totalPerformance",
              "debtForm",
              "custmizePaySearchForm",
            ];

            objectProps.forEach((prop) => {
              if (this[prop] && typeof this[prop] === "object") {
                this[prop] = null;
              }
            });

            console.log("pay component beforeDestroy - 资源清理完成");
          } catch (error) {
            console.error("清理数组和对象引用时出错:", error);
          }

          // 移除DOM元素 - 放在最后执行
          this.removeFromBody();
        } catch (error) {
          console.error("pay beforeDestroy error:", error);
        }
      },
    });
  });
});
