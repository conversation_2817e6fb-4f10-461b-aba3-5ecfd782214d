Vue.component("f-base-button", function (resolve, reject) {
  $.get("component/fBaseButton/fBaseButton.html").then(function (res) {
    resolve({
      template: res,
      props: {
        title: {
          type: String,
        },
        type: {
          type: String,
          default: "default",
        },
        size: {
          type: String,
          default: "default",
        },
      },
      data() {
        return {
          resizeObserver: null,
        };
      },
      computed: {
        typeClass: function () {
          return `cf-btn-type-${this.type} cf-btn-size-${this.size}`;
        },
      },
      watch: {
        size() {
          this.$nextTick(this.adjustBlurFilter);
        },
        type() {
          this.$nextTick(this.adjustBlurFilter);
        },
      },
      mounted() {
        this.$nextTick(() => {
          this.adjustBlurFilter();

          // 使用ResizeObserver（如果浏览器支持）
          if (typeof ResizeObserver !== "undefined") {
            this.resizeObserver = new ResizeObserver(() => {
              this.adjustBlurFilter();
            });
            this.resizeObserver.observe(this.$el);
          } else {
            // 降级方案：使用window resize事件
            window.addEventListener("resize", this.adjustBlurFilter);
          }

          // 初始化时强制执行一次
          setTimeout(this.adjustBlurFilter, 100);
        });
      },
      beforeDestroy() {
        if (this.resizeObserver) {
          this.resizeObserver.disconnect();
        }
        window.removeEventListener("resize", this.adjustBlurFilter);
      },
      methods: {
        handleClick(evt) {
          this.$emit('click', evt);
        },
        adjustBlurFilter() {
          if (this.type === "primary") {
            const btnElement = this.$el;
            if (btnElement) {
              const btnWidth = btnElement.offsetWidth;
              const blurValue = Math.max(5, btnWidth * 0.12); // 10%宽度，最小5px
              const colorMaskHeight = btnWidth * 0.7; // 高度为宽度的50%

              // 使用ref引用直接访问DOM元素
              if (this.$refs.colorMask) {
                const colorMask = this.$refs.colorMask;
                colorMask.style.filter = `blur(${blurValue}px)`;
                colorMask.style.height = `${colorMaskHeight}px`;
              } else {
                const colorMask = btnElement.querySelector(
                  ".cf-base-btn-color-mask"
                );
                if (colorMask) {
                  colorMask.style.filter = `blur(${blurValue}px)`;
                  colorMask.style.height = `${colorMaskHeight}px`;
                }
              }
            }
          }
        },
      },
    });
  });
});
