@property --cf-base-btn-border-color-1 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}
@property --cf-base-btn-border-color-2 {
  syntax: "<color>";
  initial-value: #c3cded;
  inherits: false;
}
@property --cf-base-btn-border-color-3 {
  syntax: "<color>";
  initial-value: #cbbfff;
  inherits: false;
}
@property --cf-base-btn-border-color-4 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}
@property --cf-base-btn-color-1 {
  syntax: "<color>";
  initial-value: #f8f8f8;
  inherits: false;
}
@property --cf-base-btn-color-2 {
  syntax: "<color>";
  initial-value: #f8f8f8;
  inherits: false;
}
@property --cf-base-btn-color-3 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}
@property --cf-base-btn-color-4 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}
@property --cf-base-btn-stop-1 {
  syntax: "<percentage>";
  initial-value: 0%;
  inherits: false;
}
@property --cf-base-btn-stop-2 {
  syntax: "<percentage>";
  initial-value: 30%;
  inherits: false;
}
@property --cf-base-btn-stop-3 {
  syntax: "<percentage>";
  initial-value: 70%;
  inherits: false;
}
@property --cf-base-btn-stop-4 {
  syntax: "<percentage>";
  initial-value: 100%;
  inherits: false;
}

.cf-btn-size-default {
  font-size: 16px;
}
.cf-btn-size-small {
  font-size: 14px;
}

.cf-base-btn.cf-btn-type-default.cf-btn-size-default {
  --cf-base-btn-border-color-1: #fff;
  --cf-base-btn-border-color-2: #c3cded;
  --cf-base-btn-border-color-3: #cbbfff;
  --cf-base-btn-border-color-4: #fff;
}
.cf-base-btn.cf-btn-type-default.cf-btn-size-small {
  --cf-base-btn-border-color-1: #e3e7f3;
  --cf-base-btn-border-color-2: #c3cded;
  --cf-base-btn-border-color-3: #cbbfff;
  --cf-base-btn-border-color-4: #eae7fa;
}

.cf-base-btn.cf-btn-type-light.cf-btn-size-default {
  --cf-base-btn-border-color-1: #fff;
  --cf-base-btn-border-color-2: #c3cded;
  --cf-base-btn-border-color-3: #cbbfff;
  --cf-base-btn-border-color-4: #fff;
}
.cf-base-btn.cf-btn-type-light.cf-btn-size-small {
  --cf-base-btn-border-color-1: #e3e7f3;
  --cf-base-btn-border-color-2: #c3cded;
  --cf-base-btn-border-color-3: #cbbfff;
  --cf-base-btn-border-color-4: #eae7fa;
}
.cf-base-btn.cf-btn-type-primary.cf-btn-size-small {
  --cf-base-btn-border-color-1: rgb(177, 192, 240);
  --cf-base-btn-border-color-2: #8ca0dc;
  --cf-base-btn-border-color-3: #947ef4;
  --cf-base-btn-border-color-4: rgb(187, 172, 255);
}

.cf-base-btn {
  --cf-base-btn-height-default: 51px;
  --cf-base-btn-height-small: 36px;
  --cf-base-btn-border: 2px;
  --cf-base-btn-radius-default: 12px;
  --cf-base-btn-radius-small: 6px;
  display: flex;
  justify-content: center;
  align-items: center;

  transition:
    --cf-base-btn-border-color-1 0.3s ease,
    --cf-base-btn-border-color-2 0.3s ease,
    --cf-base-btn-border-color-3 0.3s ease,
    --cf-base-btn-border-color-4 0.3s ease,
    --cf-base-btn-border-stop-1 0.3s ease,
    --cf-base-btn-border-stop-2 0.3s ease,
    box-shadow 0.3s ease,
    transform 0.2s ease;

  cursor: pointer;

  background: linear-gradient(
    180deg,
    var(--cf-base-btn-border-color-1) 0%,
    var(--cf-base-btn-border-color-2) 30%,
    var(--cf-base-btn-border-color-3) 70%,
    var(--cf-base-btn-border-color-4) 100%
  );
  padding: var(--cf-base-btn-border);
}
.cf-base-btn.cf-btn-size-default {
  border-radius: var(--cf-base-btn-radius-default);
  height: var(--cf-base-btn-height-default);
}
.cf-base-btn.cf-btn-size-default:not(.cf-btn-type-primary) {
  box-shadow:
    0px -6px 10px -10px rgba(0, 0, 0, 0.25),
    0px 14px 15px -10px #d8def0;
}
.cf-base-btn.cf-btn-size-default.cf-btn-type-primary {
  box-shadow:
    0 -6px 10px -10px #254cff,
    0 14px 30px -15px #7b9cff;
}

.cf-base-btn.cf-btn-size-small {
  border-radius: var(--cf-base-btn-radius-small);
  height: var(--cf-base-btn-height-small);
}

.cf-base-btn:not(.is-disable):not(
    .cf-btn-size-small.cf-btn-type-primary
  ):hover {
  --cf-base-btn-border-color-2: #8ca0dc;
  --cf-base-btn-border-color-3: #947ef4;
}
.cf-base-btn.cf-btn-size-small.cf-btn-type-primary:hover {
  --cf-base-btn-border-color-1: #8ca0dc;
  --cf-base-btn-border-color-2: #5e80e5;
  --cf-base-btn-border-color-3: #735bd9;
  --cf-base-btn-border-color-4: #9d8edc;
}

.cf-base-btn.cf-btn-size-default:not(.is-disable):not(
    .cf-btn-type-primary
  ):hover {
  box-shadow:
    0px -6px 10px -10px #385cffff,
    0px 14px 30px -15px #7b9cff;
}

.cf-base-btn.cf-btn-size-default.cf-btn-type-primary:hover {
  box-shadow:
    0px -6px 10px -8px rgb(28, 70, 255),
    0px 14px 30px -13px rgb(72, 118, 255);
}

.cf-base-btn:not(.is-disable):active {
  transform: translateY(2px);
}

.cf-base-btn.cf-btn-size-default:not(.is-disable):not(
    .cf-btn-type-primary
  ):active {
  box-shadow:
    0px -3px 8px -10px #385cffff,
    0px 10px 20px -15px #7b9cff;
}
.cf-base-btn.cf-btn-size-default.cf-btn-type-primary:active {
  box-shadow:
    0 -4px 8px -10px #254cff,
    0 14px 30px -15px #7b9cff;
}

.cf-btn-type-default .cf-base-btn-inner-box {
  --cf-base-btn-color-1: #f8f8f8;
  --cf-base-btn-color-2: #f8f8f8;
  --cf-base-btn-color-3: #fff;
  --cf-base-btn-color-4: #fff;
}

.cf-btn-type-light .cf-base-btn-inner-box {
  --cf-base-btn-color-1: #e9edfa;
  --cf-base-btn-color-2: #eaecfa;
  --cf-base-btn-color-3: #ebebfa;
  --cf-base-btn-color-4: #ebebfa;
}

.cf-base-btn-inner-box {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  transition:
    --cf-base-btn-color-1 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --cf-base-btn-color-2 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --cf-base-btn-color-3 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --cf-base-btn-color-4 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --cf-base-btn-stop-1 0.4s ease,
    --cf-base-btn-stop-2 0.4s ease,
    --cf-base-btn-stop-3 0.4s ease,
    --cf-base-btn-stop-4 0.4s ease,
    text-shadow 0.2s ease,
    transform 0.2s ease;
  background: linear-gradient(
    90deg,
    var(--cf-base-btn-color-1) var(--cf-base-btn-stop-1),
    var(--cf-base-btn-color-2) var(--cf-base-btn-stop-2),
    var(--cf-base-btn-color-3) var(--cf-base-btn-stop-3),
    var(--cf-base-btn-color-4) var(--cf-base-btn-stop-4)
  );
  padding: 0 20px;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.cf-btn-size-default .cf-base-btn-inner-box {
  border-radius: calc(
    var(--cf-base-btn-radius-default) - var(--cf-base-btn-border)
  );
}

.cf-btn-size-small .cf-base-btn-inner-box {
  border-radius: calc(
    var(--cf-base-btn-radius-small) - var(--cf-base-btn-border)
  );
}

.cf-base-btn.cf-btn-size-default:hover .cf-base-btn-inner-box {
  text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2);
}

.cf-base-btn.cf-btn-type-default:hover .cf-base-btn-inner-box {
  --cf-base-btn-color-1: #eceffb;
  --cf-base-btn-color-2: #eceffb;
  --cf-base-btn-color-3: #f5f6fa;
  --cf-base-btn-color-4: #fff;
}

.cf-base-btn.cf-btn-type-light:hover .cf-base-btn-inner-box {
  --cf-base-btn-color-1: #d5deff;
  --cf-base-btn-color-2: #eaecfa;
  --cf-base-btn-color-3: #ebebfa;
  --cf-base-btn-color-4: #d9d6ff;
}

.cf-base-btn.cf-btn-type-default:active .cf-base-btn-inner-box {
  --cf-base-btn-stop-2: 5%;
  --cf-base-btn-stop-3: 20%;
}

.cf-base-btn.cf-btn-type-light:active .cf-base-btn-inner-box {
  --cf-base-btn-color-1: #dce3fe;
  --cf-base-btn-color-2: #eaecfa;
  --cf-base-btn-color-3: #f3f3fc;
  --cf-base-btn-color-4: #e4e3fe;
  --cf-base-btn-stop-2: 20%;
  --cf-base-btn-stop-3: 80%;
}

.cf-base-btn.cf-btn-type-primary .cf-base-btn-text {
  z-index: 1;
  color: #fff;
}

.cf-base-btn-color-mask {
  position: absolute;
  /* 高度将由JavaScript动态设置为宽度的50% */
  /* 确保居中显示 */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
  /* 减少blur值以提升性能 */
  filter: blur(6px);
  /* 简化过渡效果，减少重绘 */
  transition:
    filter 0.2s ease,
    width 0.2s ease;
  border-radius: 100px;
  background: linear-gradient(92deg, #3363ff 0%, #775fdd 100%);
  width: 105%;
}

.cf-base-btn:hover .cf-base-btn-color-mask {
  width: 113%;
}
.cf-base-btn:active .cf-base-btn-color-mask {
  width: 130%;
}
